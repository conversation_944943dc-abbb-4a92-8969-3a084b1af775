# HTTP Library Testing Strategy - Hybrid Approach

## Executive Summary

The HTTP library is the most complex library in the system, integrating with 6 other libraries in a sophisticated web of dependencies. Based on comprehensive testing infrastructure research, this document outlines a revolutionary hybrid testing strategy combining bundle-based integration testing with traditional Jest unit testing for optimal coverage and development experience.

## 🚀 MAJOR BREAKTHROUGH: Bundle Optimization Testing

### Revolutionary Insight

**Bundle optimization (webpack) solves ALL complex testing infrastructure challenges:**

- **ESM Module Support**: Got v14 and complex dependencies work seamlessly
- **6x Performance**: 3-5s feedback vs 15-30s traditional compilation  
- **Real Environment**: Tests run in identical production webpack environment
- **Cross-Library Integration**: Validates actual dependency injection patterns
- **TypeScript Flexibility**: Bypasses strict compilation with transpileOnly mode

### Hybrid Testing Architecture Decision

**Primary Approach**: Bundle-based testing for integration scenarios  
**Secondary Approach**: Simple Jest testing for pure unit logic (researching web solutions)

## Complexity Analysis

### Multi-Dimensional Testing Matrix

| Dimension | Options | Count |
|-----------|---------|-------|
| HTTP Operations | GET, POST, PUT, DELETE, PATCH | 5 |
| Target Services | auth-service, user-service, external APIs | 3 |
| Integration Libraries | observability, resilience, caching, messaging, error-handling, auth-common | 6 |
| Error Scenarios | network errors, timeouts, 4xx/5xx responses, circuit breaker states | 6 |
| Network Conditions | normal, slow, intermittent, high-latency | 4 |

**Total Potential Scenarios: 5 × 3 × 6 × 6 × 4 = 2,160 scenarios**

### Hybrid Strategy Optimization

**Bundle-Based Integration Tests (15-20 tests)** - Real environment validation:
1. Circuit breaker shared state management  
2. Configuration precedence (enterprise defaults vs route overrides)
3. Cache + messaging coordination
4. End-to-end correlation ID propagation
5. Real HTTP operations with Got client
6. Cross-library dependency injection validation

**Simple Jest Unit Tests (10-15 tests)** - Fast development feedback:
1. HTTP utility functions (URL building, configuration merging)
2. Error handling logic and transformations
3. Request/response processing
4. Configuration parsing and validation

**E2E Bundle Tests (3-5 tests)** - Service-to-service validation:
1. Complete HTTP communication flows
2. Performance validation with HTTP/2
3. Failure cascade prevention testing

## Test Architecture

### Test Pyramid for HTTP Library

```
    E2E (5%)
   Integration (25%)
  Unit Tests (70%)
```

**Unit Tests (70%)** - Mock all external dependencies
- HTTP client configuration
- Request/response transformation
- Error handling logic
- Configuration precedence

**Integration Tests (25%)** - Real dependencies, controlled environment
- Circuit breaker state sharing
- Cache operations with Redis
- Message publishing coordination
- Authentication context flow

**E2E Tests (5%)** - Full system scenarios
- Multi-service request chains
- Performance validation
- Failure cascade scenarios

## Testing Utilities Required

### 1. HTTP Test Utilities

```typescript
// libs/testing-utils/src/http/http-test-utils.ts

export class HttpTestUtils {
  /**
   * Create HTTP service with test configuration
   */
  static createTestHttpService(config?: Partial<HttpConfig>): HttpClientService {
    return new HttpClientService({
      enterprise: {
        timeout: 1000,        // Fast timeouts for tests
        retries: 1,           // Minimal retries
        retryDelay: 100,      // Quick retry delays
        enableMetrics: false, // Disable metrics in unit tests
        ...config?.enterprise
      },
      routes: config?.routes || {}
    });
  }

  /**
   * Mock HTTP server for integration tests
   */
  static createMockServer(port: number): MockHttpServer {
    return new MockHttpServer(port)
      .onGet('/health').reply(200, { status: 'ok' })
      .onPost('/auth/login').reply(200, { token: 'test-token' })
      .onGet('/users/:id').reply(200, { id: 1, name: 'Test User' });
  }

  /**
   * Validate HTTP/2 multiplexing
   */
  static async validateHttp2Multiplexing(
    baseUrl: string,
    concurrentRequests: number = 10
  ): Promise<Http2ValidationResult> {
    const startTime = Date.now();
    
    const requests = Array(concurrentRequests).fill(0).map((_, i) =>
      httpService.get(`${baseUrl}/test/${i}`)
    );
    
    const responses = await Promise.all(requests);
    const duration = Date.now() - startTime;
    
    return {
      totalRequests: concurrentRequests,
      successfulRequests: responses.filter(r => r.status === 200).length,
      averageResponseTime: duration / concurrentRequests,
      connectionReuseDetected: this.detectConnectionReuse(responses)
    };
  }

  /**
   * Test circuit breaker state transitions
   */
  static async testCircuitBreakerIntegration(
    serviceName: string,
    httpService: HttpClientService
  ): Promise<CircuitBreakerTestResult> {
    const circuit = circuitBreakerService.getCircuitBreaker(serviceName);
    
    // 1. Verify circuit starts closed
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
    
    // 2. Trigger failures to open circuit
    const failurePromises = Array(10).fill(0).map(() =>
      httpService.get('/failing-endpoint').catch(() => null)
    );
    await Promise.all(failurePromises);
    
    // 3. Verify circuit opened
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    
    // 4. Verify HTTP service respects circuit state
    await expect(httpService.get('/any-endpoint'))
      .rejects.toThrow('Breaker is open');
    
    return {
      circuitOpenedCorrectly: true,
      httpServiceRespectedCircuit: true,
      failureThresholdMet: true
    };
  }
}
```

### 2. Integration Test Utilities

```typescript
// libs/testing-utils/src/integration/integration-test-utils.ts

export class IntegrationTestUtils {
  /**
   * Setup full HTTP stack for integration tests
   */
  static async setupHttpStack(): Promise<HttpTestStack> {
    const redis = await this.startRedis();
    const prometheus = await this.startPrometheus();
    const circuitBreaker = new CircuitBreakerService();
    
    const httpService = new HttpClientService({
      enterprise: {
        enableCaching: true,
        enableCircuitBreaker: true,
        enableMetrics: true,
        cacheConfig: { redis }
      }
    });
    
    return {
      httpService,
      redis,
      prometheus,
      circuitBreaker,
      cleanup: async () => {
        await redis.disconnect();
        await prometheus.stop();
      }
    };
  }

  /**
   * Test cache + messaging coordination
   */
  static async testCacheMessagingCoordination(
    httpService: HttpClientService,
    messagingService: MessagingService
  ): Promise<void> {
    const messageListener = jest.fn();
    messagingService.subscribe('http.cache.hit', messageListener);
    
    // First request - should miss cache and publish cache.miss
    await httpService.get('/test-endpoint');
    expect(messageListener).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'http.cache.miss',
        url: '/test-endpoint'
      })
    );
    
    messageListener.mockClear();
    
    // Second request - should hit cache and publish cache.hit
    await httpService.get('/test-endpoint');
    expect(messageListener).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'http.cache.hit',
        url: '/test-endpoint'
      })
    );
  }

  /**
   * Test configuration precedence
   */
  static testConfigurationPrecedence(): void {
    const httpService = new HttpClientService({
      enterprise: {
        timeout: 5000,
        retries: 3
      },
      routes: {
        '/auth/*': {
          timeout: 2000,  // Override for auth routes
          retries: 0      // No retries for auth
        }
      }
    });
    
    // Test enterprise defaults
    const generalConfig = httpService.getConfigForRoute('/users/123');
    expect(generalConfig.timeout).toBe(5000);
    expect(generalConfig.retries).toBe(3);
    
    // Test route overrides
    const authConfig = httpService.getConfigForRoute('/auth/login');
    expect(authConfig.timeout).toBe(2000);
    expect(authConfig.retries).toBe(0);
  }
}
```

### 3. Observability Testing Utilities

```typescript
// libs/testing-utils/src/observability/observability-test-utils.ts

export class ObservabilityTestUtils {
  /**
   * Validate HTTP request observability
   */
  static async validateHttpObservability(
    operation: () => Promise<any>,
    expectedMetrics: ExpectedMetrics
  ): Promise<ObservabilityValidation> {
    const testId = this.generateTestId();
    
    // Capture baseline metrics
    const baselineMetrics = await this.captureMetrics();
    
    // Execute operation
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    
    // Wait for observability data to propagate
    await this.waitForObservabilityPropagation(2000);
    
    // Validate metrics
    const finalMetrics = await this.captureMetrics();
    const metricsDelta = this.calculateMetricsDelta(baselineMetrics, finalMetrics);
    
    // Validate logs
    const logs = await this.waitForLogEntries({
      service: expectedMetrics.service,
      context: 'HttpClientService',
      message: new RegExp(expectedMetrics.logPattern),
      timeout: 5000
    });
    
    // Validate traces
    const traces = await this.waitForTraces({
      service: expectedMetrics.service,
      operation: expectedMetrics.operation,
      timeout: 5000
    });
    
    return {
      success: result.success,
      duration,
      metricsValidation: this.validateMetrics(metricsDelta, expectedMetrics),
      logsValidation: this.validateLogs(logs, expectedMetrics),
      tracesValidation: this.validateTraces(traces, expectedMetrics)
    };
  }

  /**
   * Wait for log entries with polling
   */
  static async waitForLogEntries(options: LogWaitOptions): Promise<LogEntry[]> {
    const pollInterval = 200;
    const maxAttempts = Math.ceil((options.timeout || 5000) / pollInterval);
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const logs = await this.queryLoki({
          service: options.service,
          context: options.context,
          message: options.message,
          since: new Date(Date.now() - 10000) // Last 10 seconds
        });
        
        if (logs.length > 0) return logs;
      } catch (error) {
        console.warn(`Log query attempt ${i + 1} failed:`, error.message);
      }
      
      await this.sleep(pollInterval);
    }
    
    throw new Error(`No logs found matching criteria within ${options.timeout}ms`);
  }

  /**
   * Query Loki for log entries
   */
  private static async queryLoki(query: LokiQuery): Promise<LogEntry[]> {
    const lokiQuery = this.buildLokiQuery(query);
    
    const response = await axios.get('http://localhost:3100/loki/api/v1/query', {
      params: {
        query: lokiQuery,
        limit: 100,
        time: Math.floor(Date.now() * 1000000) // Nanoseconds
      },
      timeout: 2000
    });
    
    return this.parseLokiResponse(response.data);
  }

  /**
   * Wait for observability data propagation
   * Accounts for winston batching, prometheus scraping, etc.
   */
  private static async waitForObservabilityPropagation(ms: number): Promise<void> {
    await this.sleep(ms);
  }
}
```

## Test Scenarios Priority Matrix

### P0 (Critical) - Must Pass Before Release

1. **Circuit Breaker Integration**
   ```typescript
   it('should share circuit breaker state between HTTP service and health endpoints', async () => {
     // Trigger circuit breaker through HTTP failures
     // Verify health endpoint shows circuit state
     // Verify HTTP service respects circuit state
   });
   ```

2. **Configuration Precedence**
   ```typescript
   it('should apply route-specific overrides over enterprise defaults', async () => {
     // Configure enterprise defaults
     // Configure route overrides
     // Verify correct config applied for each route
   });
   ```

3. **Cache + Messaging Coordination**
   ```typescript
   it('should publish cache events when caching HTTP responses', async () => {
     // Setup message listener
     // Make HTTP request (cache miss)
     // Verify cache.miss event published
     // Make same request (cache hit)
     // Verify cache.hit event published
   });
   ```

### P1 (Important) - High Value Scenarios

4. **End-to-End Correlation**
   ```typescript
   it('should propagate correlation IDs across service boundaries', async () => {
     // Start request with correlation ID
     // Make HTTP call to downstream service
     // Verify correlation ID preserved
     // Verify observability includes correlation ID
   });
   ```

5. **Authentication Context Propagation**
   ```typescript
   it('should propagate JWT context in HTTP requests', async () => {
     // Set authentication context
     // Make HTTP request
     // Verify JWT token included in request
     // Verify user context preserved
   });
   ```

### P2 (Nice to Have) - Performance and Edge Cases

6. **HTTP/2 Performance Validation**
   ```typescript
   it('should achieve expected performance improvements with HTTP/2', async () => {
     // Make concurrent requests
     // Measure connection reuse
     // Verify multiplexing benefits
     // Compare against HTTP/1.1 baseline
   });
   ```

7. **Failure Cascade Prevention**
   ```typescript
   it('should prevent failure cascades through circuit breakers', async () => {
     // Simulate downstream service failure
     // Verify circuit breaker opens
     // Verify upstream service remains healthy
     // Verify graceful degradation
   });
   ```

## Implementation Timeline

### Week 1: Foundation
- Create HTTP test utilities
- Setup integration test infrastructure
- Implement basic unit tests

### Week 2: Core Integrations
- Circuit breaker integration tests
- Cache + messaging coordination tests
- Configuration precedence validation

### Week 3: Observability
- Implement observability testing utilities
- Add HTTP observability validation
- Create performance benchmarks

### Week 4: Advanced Scenarios
- End-to-end correlation tests
- Failure cascade prevention tests
- Performance optimization validation

## Success Metrics

- **Coverage**: 90%+ line coverage on HTTP library core
- **Integration**: All 6 library integrations tested with real dependencies
- **Performance**: HTTP/2 benefits validated (60-80% improvement)
- **Reliability**: Circuit breaker protection validated under failure scenarios
- **Observability**: All HTTP operations produce expected logs, metrics, and traces

## 🔍 Research: Simple Jest Testing Solutions

### Current Challenges with Jest + ESM
1. **Got v14 ESM Module**: Cannot be easily mocked with traditional Jest approaches
2. **TypeScript Compilation**: Strict type checking causes compilation failures  
3. **Complex Dependencies**: Multiple library dependencies create circular issues
4. **Module Resolution**: @libs/* path mapping conflicts with Jest

### Web Research Areas for Simple Jest Implementation
1. **Modern Jest ESM Configuration**:
   - Jest 29+ native ESM support with `"type": "module"`
   - `--experimental-vm-modules` flag usage
   - Transform configuration for ESM modules

2. **Alternative Test Runners**:
   - **Vitest**: Vite-based test runner with superior ESM support
   - **Node.js native test runner**: Built-in testing with better ESM handling
   - **esbuild-jest**: Faster TypeScript compilation than ts-jest

3. **Module Mocking Strategies**:
   - Dynamic imports for ESM modules in tests
   - Manual mocks with `__mocks__` directories  
   - Jest module mapper refinements for @libs/* paths
   - Conditional mocking based on environment

4. **TypeScript Configuration Approaches**:
   - Separate tsconfig.test.json with relaxed settings
   - ts-jest alternatives (@swc/jest, esbuild-jest)
   - transpileOnly mode in test environment
   - Skip type checking in tests for faster execution

### Implementation Strategy
1. **Research Phase**: Test various Jest configurations and alternatives
2. **Bundle First**: Implement bundle-based tests as primary strategy  
3. **Jest Secondary**: Add simple Jest tests for pure logic functions
4. **Documentation**: Document best practices for future libraries

This hybrid approach ensures comprehensive testing coverage while providing both real environment validation and fast development feedback cycles.