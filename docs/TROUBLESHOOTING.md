# Troubleshooting Guide

This document contains solutions for common issues encountered during development and debugging.

## HTTP Client Issues

### API Gateway HTTP Client Hanging (June 2025)

**Symptoms:**
- ALL POST requests with JSON bodies through API Gateway hang for 10+ seconds and timeout
- Affects multiple endpoints: `/api/auth/login`, `/api/auth/register`, `/api/users` (POST)
- GET requests work immediately (e.g., `/api/auth/health`, `/api/users/me`)
- Direct requests to services work immediately
- No circuit breaker errors - issue occurs at Got HTTP client level

**Root Cause:**
Got HTTP client configuration issue in `@libs/http`. The hang occurs at line 620 in `http-client.service.ts`:
```typescript
response = await client(gotOptions); // Hangs here
```

**Detailed Investigation Findings:**

*Confirmed NOT the cause:*
- ❌ HTTP/2 protocol (tested with HTTP/1.1, still hangs)
- ❌ Circuit breakers (logs show "Executing request without circuit breaker")
- ❌ Request transformation (comprehensive logging shows perfect data extraction)
- ❌ Service connectivity (direct container-to-container calls work)
- ❌ Auth service specific (affects multiple services)

*Confirmed issue pattern:*
- ✅ Only POST requests with JSON bodies hang
- ✅ GET requests work fine
- ✅ Direct service calls work fine
- ✅ Request reaches Got client execution then hangs
- ✅ Got options are correctly configured with `{ json: data }`

**Got Options Analysis:**
From comprehensive debugging logs:
```
gotOptionsMethod: "POST"
gotOptionsHasJson: true (correct)
gotOptionsHasBody: false (correct - using json option)
gotOptionsJsonPreview: '{"email":"<EMAIL>","password":"testpassword"}'
gotOptionsHeadersContentType: "application/json"
gotOptionsResponseType: "json"
```

**Diagnosis Steps:**
```bash
# 1. Test POST vs GET through API Gateway
curl -X GET http://localhost:3000/api/auth/health  # Works immediately
curl -X POST http://localhost:3000/api/auth/login -H "Content-Type: application/json" -d '{"email":"test","password":"test"}'  # Hangs

# 2. Test different services
curl -X POST http://localhost:3000/api/users -H "Content-Type: application/json" -d '{"email":"test"}'  # Also hangs
curl -X POST http://localhost:3000/api/auth/register -H "Content-Type: application/json" -d '{}'  # Also hangs

# 3. Test direct services (bypassing API Gateway)
curl -X POST http://localhost:3001/auth/login -H "Content-Type: application/json" -d '{"email":"test","password":"test"}'  # Works immediately

# 4. Check detailed logs
docker logs polyrepo_api_gateway_volume | grep "About to call Got client"
# Should see log but no "Got client execution SUCCESS"
```

**Web Search Investigation Results:**

*Key findings from Got v14.4.2 research:*
- ❌ **Got has no default timeout** - requests hang forever without explicit configuration
- ❌ **Known POST + JSON issues** - Multiple GitHub issues show Got v11-v14 hanging on POST with JSON bodies
- ❌ **Docker networking problems** - Common MTU settings cause POST hangs in containers
- ❌ **Version-specific bugs** - Got 14.4.2 has known issues, latest 14.4.7 includes fixes
- ❌ **Timeout config format** - Got requires specific timeout object structure that may not be applied correctly

**SOLUTION IMPLEMENTED (June 2025) - ENTERPRISE TIMEOUT OVERHAUL:**

**ROOT CAUSE:** Got 14.4.2 timeout issues + excessive retry policies caused long hangs (8s + 3 retries = 32s worst case).

**COMPREHENSIVE FIX:**

1. **Enterprise-Grade Timeouts** in `@libs/http/src/client/http-client.service.ts`:
```typescript
timeout: {
  response: options.timeout || 3000, // Enterprise: 3s max (was 8s)
  connect: 1000,    // Enterprise: 1s connect (was 3s)
  socket: 4000      // Enterprise: 4s total (was 10s)
}
```

2. **Safe Retry Policy** - Removed dangerous operations:
```typescript
retry: {
  limit: 1,                         // 1 retry max (was 3)
  methods: ['GET', 'POST', 'PUT'],  // Removed DELETE & PATCH
  statusCodes: [408, 500, 502, 503, 504] // Server errors only
}
```

3. **Configuration Cleanup Strategy** - Two-layer architecture:

**Layer 1: @libs/http (Infrastructure)**
```typescript
// Global defaults for all services
timeout: { response: 3000, connect: 1000, socket: 4000 }
retry: { limit: 1, methods: ['GET','POST','PUT'] }
```

**Layer 2: Route Config (Business Logic)**
```typescript
// API Gateway route.config.ts - exceptions only
{ pattern: '/api/auth/login', timeout: 2000, retryLimit: 0 }
{ pattern: '/api/users/bulk', timeout: 15000, retryLimit: 0 }
```

**ELIMINATED:** 
- ❌ Individual service HTTP modules (auth/user-service)
- ❌ API Gateway service-specific configs  
- ❌ Mixed timeout values across services

**RESULT:** 
- ✅ Simple operations: <3s response time
- ✅ No dangerous retries for DELETE/PATCH operations  
- ✅ Single source of truth for infrastructure policies
- ✅ Route-specific business requirements only when needed

**Additional Solutions (if needed):**

*Medium Priority:*
1. **Test alternative body format** - Try `{ body: JSON.stringify(data) }` instead of `{ json: data }`

*Medium Priority:*
3. **Upgrade Got version** - Update from 14.4.2 to latest 14.4.7 for bug fixes
4. **Check Docker MTU settings** - Common cause of POST hangs in containerized environments
5. **Disable Got retries** - POST retry mechanisms can cause indefinite hangs

**Temporary Workaround:**
Use services directly on their ports for POST operations:
- Auth service: `http://localhost:3001/auth/*`
- User service: `http://localhost:3002/users/*`

**Architecture Notes:**
This investigation confirmed circuit breaker architecture concerns are valid for future debugging, but they are not causing the current issue. The comprehensive observability debugging successfully isolated the exact hanging point.

## Container and Infrastructure Issues

### Permission Errors in Docker

**Symptoms:**
- `EACCES: permission denied` errors
- `EROFS: read-only file system` errors

**Solution:**
```bash
cd /root/code/polyrepo
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev-volume.yml down -v
docker volume prune -f
yarn dev:cli start
```

### Network Conflicts

**Symptoms:**
- `network was found but has incorrect label`
- Port already in use errors

**Solution:**
```bash
docker network prune -f
docker system prune -f
yarn dev:cli stop && yarn dev:cli start
```

### Health Check Failures

**Symptoms:**
- All services showing as "unhealthy" in Docker
- Services appear to be running but health checks fail

**Root Cause:**
Health checks using `localhost:3000` instead of `0.0.0.0:3000` inside containers.

**Solution:**
Health checks should use `0.0.0.0:3000` for container internal networking.

## Library and Build Issues

### Library Build Failures

**Symptoms:**
- `Cannot find module @libs/*` errors
- TypeScript compilation errors across services

**Solution:**
```bash
yarn build:libs
# Wait for completion, then restart services
yarn dev:cli restart
```

### Symlink Errors (Windows)

**Symptoms:**
- `ENOENT` symlink errors
- Junction-related errors on Windows

**Solution:**
```bash
# Remove Docker junctions (Windows only)
rmdir "path\to\junction"
# Or use: dir /a | findstr JUNCTION to find them

# Clean and restart
docker-compose down -v
yarn dev:cli start
```

## Authentication and Service Issues

### Keycloak Connection Issues

**Symptoms:**
- Auth service timing out during user authentication
- `KEYCLOAK_CONNECTION_ERROR` in logs

**Diagnosis:**
```bash
# Check Keycloak connectivity
docker exec polyrepo_auth_service_volume wget -qO- http://keycloak:8080/realms/polyrepo-test

# Check client configuration
docker exec polyrepo_keycloak_dev /opt/keycloak/bin/kcadm.sh get clients -r polyrepo-test
```

**Common Solutions:**
- Verify `KEYCLOAK_BASE_URL=http://keycloak:8080` in `.env.bundling`
- Ensure correct realm name (`polyrepo-test` for development)
- Check client credentials match between Keycloak and service configuration

### Database Connection Issues

**Symptoms:**
- User service cannot connect to PostgreSQL
- `ECONNREFUSED` errors

**Solution:**
```bash
# Check PostgreSQL containers
docker ps | grep postgres

# Verify connection from service
docker exec polyrepo_user_service_volume pg_isready -h postgres-user-service -p 5432
```

## Circuit Breaker and Resilience Issues

### Circuit Breaker Status

**Check Circuit Breaker Health:**
```bash
curl http://localhost:3000/health/circuit-breakers
```

**Reset Circuit Breakers:**
```bash
# Reset specific service
curl -X POST http://localhost:3000/health/circuit-breakers/reset/auth-service

# Check services needing attention
curl http://localhost:3000/health/circuit-breakers | jq '.alerts.servicesAffected'
```

### Architecture Debugging Notes

**Circuit Breaker Complexity:**
- Circuit breakers wrap HTTP calls, making network-level debugging harder
- Multiple timeout layers (Got, circuit breaker, operation) can confuse debugging
- Consider bypassing circuit breakers during development for clearer error messages

## HTTP Configuration Issues

### Configuration Inconsistency (June 2025)

**Symptoms:**
- Different timeout values for same operations
- Mixed retry limits in logs (`retryLimit: 1` and `retryLimit: 3`)
- Some endpoints fast, others hanging
- Configuration changes not taking effect

**Root Cause:**
Multiple HTTP configuration layers creating inconsistent behavior:
- Individual service HTTP modules with old values
- API Gateway service-specific configs
- Library defaults
- Route-specific overrides

**Solution - Two-Layer Architecture:**

1. **Delete redundant service configs:**
```bash
# Remove these files
rm services/auth-service/src/http/http.module.ts
rm services/user-service/src/http/http.module.ts
```

2. **Use library defaults in services:**
```typescript
// In service app.module.ts
@Module({
  imports: [
    HttpModule.forRoot(), // Uses enterprise defaults
  ]
})
```

3. **Route overrides only for business exceptions:**
```typescript
// In API Gateway route.config.ts
{
  pattern: '/api/users/bulk',
  timeout: 15000,    // Business requirement: bulk can be slow
  retryLimit: 0      // Business requirement: never retry bulk
}
```

**Verification:**
```bash
# Check logs show consistent timeout values
docker logs polyrepo_api_gateway_volume | grep "timeout.*3000"
# Should see: timeout: { response: 3000, connect: 1000, socket: 4000 }
```

## Complete Reset Procedure

When all else fails, use this complete reset:

```bash
cd /root/code/polyrepo

# Stop everything
yarn dev:cli stop
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev-volume.yml down -v

# Clean Docker resources
docker volume prune -f
docker network prune -f
docker system prune -f

# Kill any hanging processes
pkill -f webpack
pkill -f nodemon

# Rebuild libraries
yarn build:libs

# Restart development
yarn dev:cli start
```

## Log Analysis

### Using Grafana for Log Analysis

```bash
# Access Grafana: http://localhost:3200 (admin/admin)
# Query recent errors:
{service="auth-service"} |= "error" | json | line_format "{{.level}} {{.message}}"

# Query specific correlation ID:
{correlationId="req-1234567890-abcdef"} | json | line_format "{{.timestamp}} {{.message}}"
```

### Service-Specific Log Commands

```bash
# API Gateway logs
yarn dev:cli logs api-gateway

# Auth service logs  
yarn dev:cli logs auth-service

# User service logs
yarn dev:cli logs user-service

# All webpack build logs
find infrastructure/local-dev -name "*webpack*.log" -exec tail -f {} +
```

## Environment-Specific Issues

### Development vs Docker Environment

**File Paths:**
- Development: Use `.env.local`
- Docker: Use `.env.bundling` 

**Service URLs:**
- Development: `http://localhost:3001` 
- Docker: `http://auth-service:3000`

**Database Connections:**
- Development: `localhost:5433`
- Docker: `postgres-user-service:5432`