# General Troubleshooting Principles

This document outlines general troubleshooting principles discovered during development of the polyrepo system. For specific issues, see the dedicated troubleshooting files in this directory.

## Core Troubleshooting Philosophy

### 1. **Isolation First, Integration Second**
When facing complex issues:
1. **Isolate components** - Test each part separately before testing together
2. **Verify assumptions** - Don't assume infrastructure is working
3. **Test from bottom up** - Start with basic connectivity, then add layers
4. **Remove variables** - Temporarily disable features to isolate root cause

**Example**: HTTP client issues → Test container networking → Test service health → Test HTTP library → Test business logic

### 2. **Follow the Data Flow**
Trace requests through the entire system:
1. **Entry point** - API Gateway receives request
2. **Routing** - Dynamic proxy finds route configuration  
3. **Authentication** - JWT validation if required
4. **Proxy** - HTTP client makes downstream request
5. **Response** - Service processes and returns response

**Debugging strategy**: Add logging at each step to find where the flow breaks.

### 3. **Distinguish Error Types**
Not all errors are created equal:

| Error Type | Characteristics | Debugging Approach |
|------------|----------------|-------------------|
| **Infrastructure** | Connection refused, timeouts, DNS | Check containers, networks, ports |
| **Configuration** | Module not found, env vars missing | Check environment, imports, builds |
| **Business Logic** | Wrong responses, validation errors | Check application code, data flow |
| **Integration** | Metrics conflicts, library incompatibilities | Check module interactions, versions |

### 4. **Use Progressive Enhancement**
Start minimal and add complexity:
1. **Minimal setup** - Get basic functionality working
2. **Add observability** - Logs, metrics, tracing
3. **Add resilience** - Circuit breakers, retries, caching
4. **Add features** - Business logic, optimizations

**Anti-pattern**: Adding all features at once makes debugging exponentially harder.

## Development Environment Troubleshooting

### Container and Volume Issues

#### **Container State Problems**
```bash
# Check container health and status
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Check container logs for startup issues
docker logs <container-name> --tail 50

# Inspect container file system
docker exec -it <container-name> sh
ls -la /app  # Check mounted files
env | grep -E "(NODE_ENV|SERVICE|URL)"  # Check environment
```

#### **Volume Mount Problems**
```bash
# Verify file changes are reflected in container
echo "console.log('TEST_CHANGE_$(date)')" >> src/main.ts
docker exec <container-name> cat /app/dist-webpack/main.bundle.js | grep TEST_CHANGE

# Check volume mount points
docker inspect <container-name> | jq '.[0].Mounts'

# Force volume refresh (nuclear option)
docker-compose down && docker volume prune -f && docker-compose up -d
```

#### **Build Cache Issues**
```bash
# Clear webpack build cache
rm -rf services/*/dist-webpack/*

# Clear Docker build cache
docker builder prune -f

# Rebuild with no cache
docker-compose build --no-cache <service-name>
```

### Library and Dependency Issues

#### **Shared Library Problems**
```bash
# Check library build status
ls -la libs/*/dist/

# Verify library versions in services
cd services/<service-name>
node -e "console.log(require('@libs/http/package.json').version)"

# Rebuild specific library
cd libs/<library-name> && yarn build

# Rebuild all libraries in correct order
yarn build:libs
```

#### **Node Modules Issues**
```bash
# Check for duplicate installations
find . -name "node_modules" -type d

# Verify yarn workspace linking
yarn workspaces list

# Clean and reinstall (nuclear option)
rm -rf node_modules services/*/node_modules libs/*/node_modules
yarn install
```

### Network and Connectivity Issues

#### **Service-to-Service Communication**
```bash
# Test container networking
docker exec api-gateway-container wget -qO- http://auth-service:3000/health

# Check network configuration
docker network ls
docker network inspect <network-name>

# Verify service discovery
docker exec <container> nslookup auth-service
```

#### **Port Conflicts**
```bash
# Check port usage
netstat -tuln | grep -E "(3000|3001|3002)"
lsof -i :3000

# Check Docker port mapping
docker port <container-name>
```

## Error Analysis Patterns

### 1. **Layer-by-Layer Debugging**

**Physical Layer**: Containers running, networks connected
```bash
docker ps | grep -v "Exited"
docker network inspect local_network
```

**Transport Layer**: Ports accessible, services responding
```bash
curl -v http://localhost:3000/health
telnet localhost 3000
```

**Application Layer**: Services processing requests correctly
```bash
curl -H "Authorization: Bearer $TOKEN" http://localhost:3000/api/auth/me
```

**Business Layer**: Application logic working as expected
```bash
# Check application logs for business logic errors
docker logs api-gateway | grep -E "(error|ERROR)"
```

### 2. **Error Correlation Techniques**

#### **Time-based Correlation**
```bash
# Show logs from all services around the same time
docker-compose logs --since="2m" | sort

# Show logs with timestamps
docker logs <container> -t | grep "$(date '+%Y-%m-%d %H:%M')"
```

#### **Request ID Correlation**
```bash
# Follow a request through all services
grep "req-12345" <(docker logs api-gateway) <(docker logs auth-service)

# Use correlation IDs in logs
curl -H "X-Correlation-ID: debug-$(date +%s)" http://localhost:3000/api/auth/health
```

### 3. **Performance vs Functionality Issues**

#### **Functionality Issues** (Binary: works or doesn't)
- Start with minimal test case
- Remove all non-essential features
- Test each component in isolation

#### **Performance Issues** (Gradual degradation)
- Use metrics and profiling
- Test under different loads
- Check resource constraints

## Observability and Monitoring

### Effective Logging Strategy

#### **Log Levels and Usage**
```typescript
// DEBUG: Detailed flow for development
logger.debug('About to execute HTTP request', { url, method });

// INFO: Important business events
logger.info('User authentication successful', { userId, method });

// WARN: Recoverable issues that need attention
logger.warn('Circuit breaker opened', { service, errorRate });

// ERROR: Unrecoverable issues that need immediate attention
logger.error('Database connection failed', { error: error.message });
```

#### **Structured Logging**
```typescript
// Good: Structured, searchable, correlatable
logger.info({
  message: 'HTTP request completed',
  method: 'GET',
  url: '/api/users',
  statusCode: 200,
  duration: 123,
  correlationId: 'req-12345'
});

// Bad: Unstructured, hard to search
logger.info(`GET /api/users returned 200 in 123ms`);
```

### Metrics and Health Checks

#### **Essential Health Checks**
```typescript
// Service health
app.get('/health', () => ({
  status: 'ok',
  timestamp: new Date().toISOString(),
  version: process.env.npm_package_version
}));

// Dependency health
app.get('/health/deep', async () => ({
  status: 'ok',
  dependencies: {
    database: await checkDatabase(),
    redis: await checkRedis(),
    downstreamServices: await checkDownstreamServices()
  }
}));
```

#### **Key Metrics to Monitor**
- **Request rates**: requests/second by endpoint
- **Error rates**: 4xx/5xx responses by endpoint  
- **Response times**: p50, p95, p99 latencies
- **Resource usage**: CPU, memory, disk I/O
- **Business metrics**: user actions, conversion rates

## Common Anti-Patterns and Solutions

### ❌ **Anti-Pattern: Log and Ignore**
```typescript
// Bad
try {
  await riskyOperation();
} catch (error) {
  logger.error('Something went wrong', error);
  // Continue as if nothing happened
}
```

### ✅ **Solution: Handle Appropriately**
```typescript
// Good
try {
  await riskyOperation();
} catch (error) {
  logger.error('Risky operation failed', { error: error.message, context });
  
  // Choose appropriate response:
  if (error.isRetryable) {
    return await retryOperation();
  } else {
    throw new ServiceUnavailableError('Service temporarily unavailable');
  }
}
```

### ❌ **Anti-Pattern: Silent Failures**
```typescript
// Bad
const result = await httpClient.get('/api/data').catch(() => null);
if (result) {
  return result.data;
}
return {}; // Silent failure
```

### ✅ **Solution: Explicit Error Handling**
```typescript
// Good
try {
  const result = await httpClient.get('/api/data');
  return result.data;
} catch (error) {
  logger.warn('Failed to fetch data, using fallback', { error: error.message });
  return await getFallbackData();
}
```

### ❌ **Anti-Pattern: Generic Error Messages**
```typescript
// Bad
throw new Error('Internal server error');
```

### ✅ **Solution: Specific, Actionable Errors**
```typescript
// Good
throw new DatabaseConnectionError(
  'Unable to connect to user database after 3 retries',
  { 
    host: config.database.host,
    retryCount: 3,
    lastError: connectionError.message,
    correlationId 
  }
);
```

## Escalation and Documentation

### When to Escalate Issues

#### **Immediate Escalation** (P0)
- Complete service outage
- Data corruption or loss
- Security breaches

#### **High Priority** (P1)
- Significant performance degradation
- Intermittent failures affecting users
- Critical feature failures

#### **Normal Priority** (P2)
- Minor bugs in non-critical features
- Performance optimizations
- Development workflow issues

### Documentation Requirements

#### **For Each Issue Resolved**
Create troubleshooting entry if:
- Resolution took > 15 minutes
- Issue likely to recur
- Solution was non-obvious
- Error messages were unclear

#### **Troubleshooting Entry Template**
```markdown
## Issue: [Brief Description]

### Symptoms
- What users/developers experience
- Exact error messages
- When it occurs

### Environment
- OS, service, dependencies where issue occurs
- Configuration that triggers the issue

### Root Cause
- Why this happens
- Contributing factors

### Solution
- Step-by-step resolution
- Commands or code changes required

### Prevention
- How to avoid in future
- Monitoring or alerts to add
- Code patterns to follow/avoid
```

## Testing and Validation

### Systematic Testing Approach

#### **1. Unit Level**
Test individual components in isolation:
```bash
# Test individual library
cd libs/http && yarn test

# Test specific service
cd services/auth-service && yarn test
```

#### **2. Integration Level**
Test service interactions:
```bash
# Test service-to-service communication
curl http://localhost:3000/api/auth/health

# Test with real dependencies
cd services/auth-service && yarn test:integration
```

#### **3. System Level**
Test complete user flows:
```bash
# Test end-to-end user journey
cd services/api-gateway && yarn test:e2e
```

### Validation Checklist

Before declaring an issue "resolved":
- [ ] Root cause identified and documented
- [ ] Fix applied and tested
- [ ] No regression in other functionality
- [ ] Monitoring in place to detect recurrence
- [ ] Documentation updated
- [ ] Team informed if issue affects others

## Specific Issue Troubleshooting

### Prisma Bundling and Database Setup Issues

#### **Issue: Deep Prisma Bundling Errors in Webpack**

**Symptoms:**
- Error: `Invalid 'r=Object.create()' invocation` in webpack bundle
- Error: `Right-hand side of 'instanceof' is not an object`  
- User Service fails to start with Prisma-related bundling errors
- Database operations fail with module resolution errors

**Environment:**
- Node.js 22 with webpack bundling
- NestJS service with Prisma ORM
- Docker Compose development environment
- Bundle optimization mode active

**Root Cause:**
Prisma Client includes native binaries and engine files that cannot be properly bundled by webpack. When Prisma engines are bundled, they lose their binary nature and become invalid JavaScript objects, causing instanceof checks to fail.

**Solution:**

1. **Update webpack.config.js externals configuration:**
```javascript
externals: [
  // ... existing externals ...
  '@prisma/client',               // Prisma ORM client (large, has native binaries)
  'prisma',                       // Prisma CLI tools
  '@prisma/engines',              // Prisma engines (binaries)
  '@prisma/engines-version',      // Prisma engines version
  // External reference to generated Prisma client to avoid bundling conflicts
  ({request}, callback) => {
    if (request.includes('generated/prisma-client')) {
      return callback(null, 'commonjs ' + request);
    }
    // Externalize any Prisma engine requests
    if (request.includes('@prisma/engines') || request.includes('schema-engine') || request.includes('query-engine')) {
      return callback(null, 'commonjs ' + request);
    }
    callback();
  },
],
```

2. **Update Prisma service imports to use standard client:**
```typescript
// In prisma.service.ts - use standard @prisma/client
import { PrismaClient } from '@prisma/client';
// NOT: import { PrismaClient } from '../generated/prisma-client';
```

3. **Run database migrations using documented approach:**
```bash
# From user-service directory, using Docker environment credentials
cd services/user-service
DATABASE_URL="postgresql://devuser_user_service:devpassword_user_service@localhost:5433/polyrepo_user_db?schema=public" npx prisma migrate reset --force
```

4. **Verify setup works:**
```bash
# Check health endpoint
curl http://localhost:3002/health

# Check database connectivity  
curl http://localhost:3002/users
```

**Prevention:**
- Always externalize ORM clients with native binaries in webpack config
- Use standard Prisma client imports, not generated paths
- Follow documented database migration procedures using proper environment configuration
- Test database setup as part of development workflow startup

#### **Issue: Database Migration Authentication Failures**

**Symptoms:**
- Error: `P3005: The database schema is not empty` during migration
- Authentication failed for user during Prisma migrate
- Cannot connect to database from migration scripts

**Environment:**
- Docker Compose with separate database containers
- Different credentials for local vs Docker environments

**Root Cause:**
Migration commands use incorrect database credentials or connection strings, often mixing local development credentials with Docker environment setup.

**Solution:**

1. **Verify Docker Compose database configuration:**
```bash
# Check database container is running
docker ps | grep postgres

# Verify credentials from .env.docker
cat services/user-service/.env.docker | grep DATABASE_URL
```

2. **Use correct connection string for migrations:**
```bash
# From CLAUDE.md documented approach
cd services/user-service
DATABASE_URL="postgresql://devuser_user_service:devpassword_user_service@localhost:5433/polyrepo_user_db?schema=public" npx prisma migrate reset --force
```

3. **Verify database state after migration:**
```bash
# Check tables were created
docker exec polyrepo_postgres_user_service_dev psql -U devuser_user_service -d polyrepo_user_db -c "\dt"
```

**Prevention:**
- Always use documented database credentials from CLAUDE.md
- Use .env.docker file for Docker environment configuration
- Test migration setup as part of development environment startup
- Document any changes to database configuration in session notes

#### **Issue: Circuit Breakers Causing Silent HTTP Failures**

**Symptoms:**
- API Gateway returns 500 "Internal proxy error" with "No response received from backend service"
- Backend services are healthy and responding correctly when tested directly
- HttpClientService returns `null/undefined` responses despite successful HTTP calls
- No clear error messages indicating circuit breaker issues
- Requests work when circuit breakers are bypassed

**Environment:**
- NestJS services with @libs/resilience circuit breakers (Opossum)
- HttpClientService with circuit breaker integration
- After previous service failures or during development restarts

**Root Cause:**
Circuit breakers enter OPEN state due to previous failures but fail silently instead of providing clear error messages. When OPEN, Opossum's `circuit.execute()` returns `undefined` rather than throwing an error, causing the HttpClientService to appear to succeed but return no data.

**Debugging Process:**
1. Check if HttpClientService is being called: Look for "BUNDLE_OPTIMIZED request()" logs
2. Check circuit breaker execution: Look for "About to call circuit.execute()" logs
3. Check if callback executes: Look for "Inside circuit breaker callback" logs
4. If callback never executes, circuit breaker is OPEN

**Solution:**

1. **Immediate Fix - Reset Circuit Breakers:**
```bash
# Reset via API endpoint (once implemented)
curl -X POST http://localhost:3000/health/circuit-breakers/reset

# Or restart services to reset circuit breaker state
yarn dev:cli stop && yarn dev:cli start
```

2. **Check Circuit Breaker State:**
```bash
# Check circuit breaker status via health endpoint
curl http://localhost:3000/health/circuit-breakers
```

3. **Bypass for Emergency:**
```typescript
// Temporary bypass in HttpClientService.request()
if (false && this.circuitBreakerService && serviceName !== 'default') {
  // Circuit breaker code...
} else {
  // Direct execution path
}
```

**Prevention Mechanisms Implemented:**

1. **Enhanced Error Messages:**
   - Clear, actionable error messages when circuit breakers are OPEN
   - Specific reset instructions included in error responses
   - Circuit breaker state and metrics logged with errors

2. **Comprehensive Monitoring:**
   - Circuit breaker summary in `/health/detailed` endpoint
   - Enhanced `/health/circuit-breakers` endpoint with alerts and troubleshooting guidance
   - Health summary includes open services and warnings

3. **Proactive Alerting:**
   - Health checks now report `degraded` status when circuit breakers are OPEN
   - Detailed troubleshooting steps and possible causes in API responses
   - Services needing attention clearly identified

4. **Event Publishing:**
   - Circuit breaker state changes published to messaging system
   - Reset events published for monitoring and alerting systems
   - Integration with observability stack for tracking

5. **Development Workflow Integration:**
   - Circuit breaker status visible in health endpoints
   - Reset capabilities via API endpoints
   - Clear documentation and debugging steps

**Warning Signs:**
- API Gateway errors after service restarts
- "No response received from backend service" without clear cause
- Services healthy when tested directly but failing through API Gateway
- HTTP requests appearing to succeed but returning undefined

## Related Documentation

- [ESM + Webpack + Got Troubleshooting](./TROUBLESHOOTING-ESM-WEBPACK-GOT.md) - Specific webpack and HTTP client issues
- [HTTP Performance Optimization](./HTTP-PERFORMANCE-OPTIMIZATION-GUIDE.md) - Performance-related troubleshooting
- [Cross-Library Integration Matrix](./CROSS-LIBRARY-INTEGRATION-MATRIX.md) - Library interaction issues
- [Development Setup](../infrastructure/local-dev/DEVELOPMENT.md) - Environment setup troubleshooting