# Clean Configuration Architecture: Preserve Current Pattern

## Current Architecture Analysis ✅

### What We Have (<PERSON> Pattern)
```
/libs/nestjs-common/src/http/
├── http.module.ts           # Generic, no service knowledge
├── http-client.service.ts   # Generic HTTP client
└── http-observability.*     # Generic observability

services/auth-service/src/http/
└── http.module.ts           # Service-specific configuration

services/user-service/src/http/  
└── http.module.ts           # Service-specific configuration
```

### Current Pattern Benefits
✅ **No coupling**: Core library has zero service-specific knowledge
✅ **No changes**: Adding services doesn't modify core library
✅ **Clean separation**: Configuration lives where it's used
✅ **Flexible**: Each service configures exactly what it needs

## New Got Architecture: Preserve the Same Pattern

### Core Library (Generic, No Service Knowledge)
```
/libs/http/
├── src/
│   ├── client/
│   │   ├── http-client.service.ts     # Generic Got wrapper
│   │   └── http-client.interfaces.ts  # Generic interfaces
│   ├── configuration/
│   │   ├── http-config.types.ts       # Configuration types only
│   │   └── http-config.defaults.ts    # Sensible defaults only
│   ├── observability/
│   │   ├── http-hooks.factory.ts      # Generic observability hooks
│   │   └── http-metrics.service.ts    # Generic metrics
│   ├── http.module.ts                 # Generic module, no service knowledge
│   └── index.ts
└── README.md
```

### Service-Level Configuration (Same as Current)
```
services/auth-service/src/http/
└── http.module.ts           # Auth-specific Got configuration

services/user-service/src/http/
└── http.module.ts           # User-specific Got configuration

services/api-gateway/src/http/
└── http.module.ts           # Gateway-specific Got configuration
```

## Implementation: Clean Got Module

### Core Library (Zero Service Knowledge)

```typescript
// /libs/http/src/http.module.ts
@Module({})
export class HttpModule {
  // Simple registration with defaults
  static register(options: HttpModuleOptions = {}): DynamicModule {
    return {
      module: HttpModule,
      providers: [
        {
          provide: 'HTTP_MODULE_OPTIONS',
          useValue: { ...DEFAULT_HTTP_OPTIONS, ...options }
        },
        HttpClientService,
        HttpObservabilityHooksFactory
      ],
      exports: [HttpClientService]
    };
  }

  // Async registration (same pattern as current)
  static registerAsync(options: HttpModuleAsyncOptions): DynamicModule {
    return {
      module: HttpModule,
      imports: options.imports || [],
      providers: [
        {
          provide: 'HTTP_MODULE_OPTIONS',
          useFactory: options.useFactory,
          inject: options.inject || []
        },
        HttpClientService,
        HttpObservabilityHooksFactory
      ],
      exports: [HttpClientService]
    };
  }
}
```

### Generic Configuration Types (No Service Names)

```typescript
// /libs/http/src/configuration/http-config.types.ts
export interface HttpModuleOptions {
  // Connection settings
  http2?: boolean;
  keepAlive?: boolean;
  
  // Timeout configuration
  timeout?: {
    lookup?: number;
    connect?: number;
    secureConnect?: number;
    socket?: number;
    send?: number;
    response?: number;
  };

  // Retry configuration
  retry?: {
    limit?: number;
    methods?: string[];
    statusCodes?: number[];
    calculateDelay?: (context: RetryContext) => number;
  };

  // Cache configuration
  cache?: {
    enabled?: boolean;
    shared?: boolean;
    maxSize?: number;
    defaultTtl?: number;
  };

  // Service-specific client configurations
  services?: Record<string, ServiceHttpConfig>;

  // Observability
  observability?: {
    enabled?: boolean;
    includeHeaders?: boolean;
    includeBody?: boolean;
  };
}

export interface ServiceHttpConfig {
  baseUrl?: string;
  timeout?: Partial<TimeoutConfig>;
  retry?: Partial<RetryConfig>;
  cache?: Partial<CacheConfig>;
  headers?: Record<string, string>;
}
```

### Generic Defaults (No Service Assumptions)

```typescript
// /libs/http/src/configuration/http-config.defaults.ts
export const DEFAULT_HTTP_OPTIONS: HttpModuleOptions = {
  http2: true,
  keepAlive: true,
  
  timeout: {
    lookup: 100,
    connect: 1000,
    secureConnect: 1000,
    socket: 5000,
    send: 10000,
    response: 5000
  },

  retry: {
    limit: 3,
    methods: ['GET', 'PUT', 'HEAD', 'DELETE', 'OPTIONS', 'TRACE'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504],
    calculateDelay: ({ attemptCount }) => Math.min(Math.pow(2, attemptCount - 1) * 1000, 30000)
  },

  cache: {
    enabled: true,
    shared: false,
    maxSize: 1000,
    defaultTtl: 300000
  },

  observability: {
    enabled: true,
    includeHeaders: false,
    includeBody: false
  }
};
```

### Generic HTTP Client (Service-Agnostic)

```typescript
// /libs/http/src/client/http-client.service.ts
@Injectable()
export class HttpClientService {
  private gotInstance: Got;
  private serviceClients = new Map<string, Got>();

  constructor(
    @Inject('HTTP_MODULE_OPTIONS') private options: HttpModuleOptions,
    private observabilityHooks: HttpObservabilityHooksFactory
  ) {
    this.gotInstance = this.createDefaultClient();
  }

  private createDefaultClient(): Got {
    return got.extend({
      http2: this.options.http2,
      timeout: this.options.timeout,
      retry: this.options.retry,
      cache: this.options.cache?.enabled ? new Map() : false,
      hooks: this.observabilityHooks.createHooks()
    });
  }

  // Generic methods (no service knowledge)
  async get<T>(url: string, options?: RequestOptions): Promise<T> {
    const response = await this.gotInstance.get(url, this.adaptOptions(options));
    return response.body;
  }

  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<T> {
    const response = await this.gotInstance.post(url, {
      ...this.adaptOptions(options),
      json: data
    });
    return response.body;
  }

  // Service client factory (configured by service)
  createServiceClient(serviceName: string, config?: ServiceHttpConfig): Got {
    if (this.serviceClients.has(serviceName)) {
      return this.serviceClients.get(serviceName)!;
    }

    const serviceConfig = {
      ...this.options.services?.[serviceName],
      ...config
    };

    const client = got.extend({
      ...this.createDefaultClient().defaults.options,
      prefixUrl: serviceConfig.baseUrl,
      timeout: { ...this.options.timeout, ...serviceConfig.timeout },
      retry: { ...this.options.retry, ...serviceConfig.retry },
      headers: serviceConfig.headers
    });

    this.serviceClients.set(serviceName, client);
    return client;
  }
}
```

## Service-Level Configuration (Same Pattern as Current)

### Auth Service HTTP Module

```typescript
// services/auth-service/src/http/http.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule as CoreHttpModule } from '@libs/http';

/**
 * HTTP module for Auth Service
 * Configures Got HTTP client for auth-specific needs:
 * - Fast timeouts for auth operations
 * - Minimal retries (fail fast)
 * - No caching for auth responses
 * - Keycloak-specific configuration
 */
@Module({
  imports: [
    CoreHttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        // Auth service optimizations
        timeout: {
          response: configService.get<number>('AUTH_HTTP_TIMEOUT', 3000)
        },
        
        retry: {
          limit: configService.get<number>('AUTH_HTTP_RETRY_LIMIT', 2)
        },
        
        cache: {
          enabled: false // Never cache auth responses
        },

        // Service-specific clients
        services: {
          keycloak: {
            baseUrl: configService.get('KEYCLOAK_BASE_URL'),
            timeout: { response: 5000 },
            retry: { limit: 2 },
            headers: {
              'User-Agent': 'AuthService/1.0'
            }
          },
          
          'user-service': {
            baseUrl: configService.get('USER_SERVICE_URL'),
            timeout: { response: 3000 },
            retry: { limit: 3 }
          }
        }
      }),
      inject: [ConfigService]
    })
  ],
  exports: [CoreHttpModule]
})
export class HttpModule {}
```

### User Service HTTP Module

```typescript
// services/user-service/src/http/http.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule as CoreHttpModule } from '@libs/http';

/**
 * HTTP module for User Service
 * Configures Got HTTP client for user-specific needs:
 * - Standard timeouts
 * - Aggressive caching for user data
 * - External API integrations
 */
@Module({
  imports: [
    CoreHttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        // User service optimizations
        cache: {
          enabled: true,
          defaultTtl: configService.get<number>('USER_CACHE_TTL', 60000)
        },

        // Service-specific clients
        services: {
          'notification-service': {
            baseUrl: configService.get('NOTIFICATION_SERVICE_URL'),
            timeout: { response: 2000 },
            retry: { limit: 1 } // Notifications not critical
          },
          
          'external-crm': {
            baseUrl: configService.get('CRM_API_URL'),
            timeout: { response: 10000 }, // External APIs can be slow
            retry: { limit: 5 },
            headers: {
              'Authorization': `Bearer ${configService.get('CRM_API_KEY')}`
            }
          }
        }
      }),
      inject: [ConfigService]
    })
  ],
  exports: [CoreHttpModule]
})
export class HttpModule {}
```

### API Gateway HTTP Module

```typescript
// services/api-gateway/src/http/http.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule as CoreHttpModule } from '@libs/http';

/**
 * HTTP module for API Gateway
 * Configures Got HTTP client for gateway-specific needs:
 * - Multiple downstream services
 * - Health check optimizations
 * - Service discovery integration
 */
@Module({
  imports: [
    CoreHttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        // Gateway optimizations
        http2: true, // Enable HTTP/2 for service communication
        
        // Multiple downstream services
        services: {
          'auth-service': {
            baseUrl: configService.get('AUTH_SERVICE_URL'),
            timeout: { response: 3000 },
            retry: { limit: 2 }
          },
          
          'user-service': {
            baseUrl: configService.get('USER_SERVICE_URL'),
            timeout: { response: 5000 },
            retry: { limit: 3 },
            cache: { enabled: true, defaultTtl: 30000 }
          },
          
          'notification-service': {
            baseUrl: configService.get('NOTIFICATION_SERVICE_URL'),
            timeout: { response: 2000 },
            retry: { limit: 1 }
          }
        }
      }),
      inject: [ConfigService]
    })
  ],
  exports: [CoreHttpModule]
})
export class HttpModule {}
```

## Usage in Services (Same as Current)

```typescript
// services/auth-service/src/auth/services/keycloak.service.ts
@Injectable()
export class KeycloakService {
  private keycloakClient: Got;

  constructor(private httpClient: HttpClientService) {
    // Get service-specific client configured in http.module.ts
    this.keycloakClient = this.httpClient.createServiceClient('keycloak');
  }

  async authenticateUser(email: string, password: string) {
    // Use pre-configured client (baseUrl, timeout, retry all set)
    return this.keycloakClient.post('/realms/myapp/protocol/openid-connect/token', {
      form: {
        grant_type: 'password',
        client_id: this.clientId,
        username: email,
        password: password
      }
    });
  }
}
```

## Benefits of This Clean Architecture

### ✅ **Zero Coupling**
- Core library has no service knowledge
- Adding new services doesn't change core library
- Each service configures only what it needs

### ✅ **Same Pattern as Current**
- Familiar structure for developers
- Easy migration from current HTTP module
- Consistent with our architectural principles

### ✅ **Maximum Flexibility**
- Services can override any setting
- Environment-specific configuration per service
- Service-specific client creation

### ✅ **Performance Benefits**
- Got's advanced features available
- HTTP/2, caching, intelligent retry
- Service-optimized configurations

### ✅ **Easy Maintenance**
- Configuration lives where it's used
- No central configuration to maintain
- Clear separation of concerns

## Migration Path

### 1. **Replace Core Library**
```typescript
// Before
import { HttpModule } from '@libs/nestjs-common';

// After  
import { HttpModule } from '@libs/http';
```

### 2. **Update Service HTTP Modules** (Same Structure)
```typescript
// Structure stays the same, just new options
// services/*/src/http/http.module.ts
```

### 3. **Update Usage** (Minimal Changes)
```typescript
// Method signatures similar, enhanced capabilities
```

This approach preserves our clean architecture while gaining all of Got's benefits! 🎉