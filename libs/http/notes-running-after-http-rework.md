# Runtime Issues After HTTP Rework

**Date**: 2025-06-12  
**Context**: Post HTTP migration from Axios to Got, Node 22 upgrade  
**Status**: Temporary workaround applied, needs future improvement

## 🚨 **Critical Runtime Issue: Module Registration**

### **Problem**
When starting API Gateway after HTTP rework, encountered dependency injection failure:

```
ERROR [ExceptionHandler] Nest can't resolve dependencies of the HttpClientService 
(HTTP_MODULE_OPTIONS, ?, CircuitBreakerService, ErrorResponseBuilderService). 
Please make sure that the argument ObservabilityLogger at index [1] is available 
in the HttpModule context.
```

### **Root Cause Analysis**

#### **1. registerAsync() vs register() Issue**
- **BEFORE**: Used `CoreHttpModule.registerAsync()` with `ConfigService` injection
- **ISSUE**: Runtime dependency resolution problems with bundled webpack builds
- **SUSPECT**: Async factory functions may not resolve properly in webpack bundles

#### **2. Dependency Chain Complexity**
```typescript
HttpClientService depends on:
├── HTTP_MODULE_OPTIONS (✅ resolved)
├── ObservabilityLogger (❌ failed resolution)
├── CircuitBreakerService (depends on ObservabilityLogger)
└── ErrorResponseBuilderService (depends on ObservabilityLogger)
```

### **Temporary Workaround Applied**

#### **Changed from Async to Static Registration**
```typescript
// BEFORE: Dynamic async registration
CoreHttpModule.registerAsync({
  imports: [ConfigModule],
  useFactory: (configService: ConfigService): HttpModuleOptions => ({
    // Dynamic config using environment variables
  }),
  inject: [ConfigService],
})

// AFTER: Static registration (temporary fix)
CoreHttpModule.register({
  // Hardcoded config with process.env fallbacks
  global: {
    http2: true,
    responseTimeout: 30000,
    connectTimeout: 3000,
    retryLimit: 1,
    defaultHeaders: {
      'x-forwarded-by': 'api-gateway',
      'x-client-version': '1.0.0',
    },
  },
  services: {
    'auth-service': {
      baseURL: process.env.AUTH_SERVICE_URL || 'http://auth-service:3001',
      responseTimeout: 8000,
      retryLimit: 1,
    },
    'user-service': {
      baseURL: process.env.USER_SERVICE_URL || 'http://user-service:3002', 
      responseTimeout: 8000,
      retryLimit: 1,
    },
  },
})
```

## 🔧 **Impact Assessment**

### **✅ What Works Now**
- API Gateway starts successfully
- Got HTTP/2 client functions
- Service-specific configurations work
- Basic environment variable support via `process.env`

### **❌ What's Lost (Temporarily)**
- Dynamic ConfigService integration
- Environment-specific timeout configurations  
- Runtime configuration validation
- Clean separation of config concerns

## 🎯 **Future Improvements Needed**

### **1. Investigate Bundle Compatibility** 
- **Issue**: Webpack bundling may interfere with NestJS async module registration
- **Solution**: Research webpack module resolution for async factories
- **Priority**: High (affects other services too)

### **2. Enhanced Module Architecture**
```typescript
// FUTURE: Better async registration
CoreHttpModule.forRoot({
  configKey: 'http', // Use ConfigService key
  services: ['auth-service', 'user-service'],
  global: {
    http2: true,
    // Environment-aware defaults
  }
})
```

### **3. Bundle-Aware Registration Pattern**
- Create bundle-detection logic
- Fallback to static registration in bundled environments
- Maintain async registration for non-bundled environments

### **4. Configuration Validation**
```typescript
// FUTURE: Runtime config validation
HttpConfigValidator.validate({
  services: requiredServices,
  timeouts: validationRules,
  environment: currentEnv
})
```

## 📋 **Action Items for Future Sessions**

### **Short Term (Next Session)**
- [ ] Test API Gateway functionality with static registration
- [ ] Verify Got HTTP/2 performance works
- [ ] Test dynamic routing and auth flow

### **Medium Term (1-2 Sessions)**
- [ ] Research webpack + NestJS async module compatibility  
- [ ] Implement bundle-aware registration pattern
- [ ] Restore ConfigService integration properly

### **Long Term (Future Sprints)**
- [ ] Create standardized module registration pattern for all services
- [ ] Add comprehensive configuration validation
- [ ] Document best practices for bundled NestJS applications

## 🚨 **Known Limitations**

### **Current Static Configuration**
- **Environment Variables**: Limited to `process.env` direct access
- **Type Safety**: Reduced (no ConfigService validation)
- **Flexibility**: Static timeouts instead of environment-specific

### **Services Affected**
- ✅ **API Gateway**: Fixed with static registration
- ⚠️  **Auth Service**: May have similar issues if using async registration  
- ⚠️  **User Service**: May have similar issues if using async registration

## 🔍 **Debugging Information**

### **Key Dependencies**
```
@libs/http depends on:
├── @libs/observability (ObservabilityLogger)
├── @libs/resilience (CircuitBreakerService) 
├── @libs/error-handling (ErrorResponseBuilderService)
└── @nestjs/config (ConfigService - problematic in async mode)
```

### **Module Import Chain**
```
API Gateway App.module
├── HTTP Module (service-specific)
│   └── @libs/http CoreHttpModule
│       ├── ObservabilityModule ✅
│       ├── CircuitBreakerModule ✅  
│       ├── ErrorHandlingModule ✅
│       └── ConfigModule ❌ (async resolution issue)
```

### **Error Pattern**
- Async module factories fail to resolve in webpack bundles
- Static registration works fine
- Pattern may affect other libraries using async registration

---

**Resolution**: Temporary static registration applied. Full investigation and proper async support planned for future sessions.

**Testing Status**: Ready for integration testing with current workaround.