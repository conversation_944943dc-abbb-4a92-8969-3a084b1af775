# HTTP Architecture Rework Plan

**Date**: 2025-06-11  
**Status**: Phase 1 Complete, Phase 2 In Progress  
**Goal**: Migrate from Axios/jwks-rsa to Got HTTP/2 with clean architecture

## 🎯 **Project Overview**

Comprehensive migration from fragmented HTTP handling to unified Got-based architecture with:
- **60-80% performance improvement** via HTTP/2
- **Centralized service configuration** (no hardcoded service knowledge in core libs)
- **Dynamic proxy routing** (eliminate multiple controllers per service)
- **Unified error handling & observability**

## ✅ **Phase 1: Core Library Architecture - COMPLETED**

### **1.1 Fixed @libs/http Architecture**
- **❌ BEFORE**: Hardcoded service configs in core library (`forGateway()`, `forRoot()`)
- **✅ AFTER**: Generic core library, service-specific configurations per service

```typescript
// REMOVED from @libs/http/src/http.module.ts
static forGateway(): DynamicModule {
  // Hardcoded auth-service and user-service configs - WRONG!
}

// NOW: Each service declares its own dependencies
// services/api-gateway/src/http/http.module.ts - service-specific config
```

### **1.2 Created Service-Specific HTTP Modules**
- **API Gateway**: `services/api-gateway/src/http/http.module.ts`
  - Configures auth-service and user-service clients
  - Gateway-optimized timeouts and retry policies
  - HTTP/2 enabled for service-to-service communication

### **1.3 Centralized Libraries Integration**
- **✅ @libs/keycloak-client**: Centralized Keycloak HTTP operations with Got
- **✅ @libs/auth-common**: Updated to use centralized Keycloak client
- **✅ @libs/http**: BaseServiceClient pattern eliminates HTTP boilerplate
- **✅ Auth Service**: Reduced from 748 lines to ~250 lines using centralized clients

## ✅ **Phase 2: API Gateway Rework - COMPLETED**

### **2.1 Current Controller Problems Identified**
Based on `analysis-got-migration-comprehensive-impact.md`:

```typescript
// CURRENT PROBLEM: Multiple controllers for same routes
@Controller('api/auth')           // AuthProxyController - unprotected
@Controller('api/auth')           // AuthProtectedProxyController - protected 
@Controller('api/users')          // UsersProxyController - protected

// Issues:
// ❌ Duplicate route patterns ('/api/auth' used twice)
// ❌ JWT logic scattered across multiple controllers  
// ❌ Hard to maintain - need 2 controllers per service
// ❌ Route configuration not centralized
// ❌ Auth logic mixed with proxy logic
```

### **2.2 Planned Solution: Dynamic Controller**

```typescript
// SOLUTION: Single dynamic controller with declarative routing
@Controller('api')
export class DynamicProxyController {
  @All('*')
  async handleAllRequests(@Req() req: Request, @Res() res: Response) {
    const routeConfig = this.routeConfigService.getRouteConfig(req.path);
    
    // Dynamic auth check based on route configuration
    if (routeConfig.requiresAuth) {
      await this.validateJwtToken(req.headers.authorization);
    }
    
    // Enhanced proxy with Got (HTTP/2, retry, caching)
    return this.enhancedProxyService.proxyRequest(routeConfig.serviceName, req, res);
  }
}

// Centralized route configuration
const routeConfig = [
  { pattern: '/api/auth/login', service: 'auth-service', requiresAuth: false },
  { pattern: '/api/auth/logout', service: 'auth-service', requiresAuth: true },
  { pattern: '/api/users', service: 'user-service', requiresAuth: false, cacheable: true },
  { pattern: '/api/users/profile', service: 'user-service', requiresAuth: true },
];
```

**Impact**: 75% reduction in controller code, centralized route management

### **2.3 Redundancy Cleanup - COMPLETED**

#### **✅ Correlation Handling Fixed**
```typescript
// BEFORE: Two correlation middlewares active
// 1. services/api-gateway/src/correlation/correlation.middleware.ts (custom) - REMOVED
// 2. @libs/error-handling CorrelationIdMiddleware (centralized) - KEPT

// RESULT: Using centralized correlation handling only
```

#### **✅ Auth Middleware Cleaned Up**
```typescript
// BEFORE: services/api-gateway/src/auth/auth-middleware.ts - REMOVED
// - Old jwks-rsa approach eliminated
// - Controllers now use @libs/auth-common JwtAuthGuard via dynamic controller

// RESULT: Centralized auth handling via dynamic controller
```

#### **✅ Error Handling Centralized**
```typescript
// BEFORE: proxy.service.ts custom error handling
// AFTER: Using ErrorResponseBuilderService from @libs/error-handling

const errorResponse = this.errorResponseBuilder.buildFromException(
  error,
  '/api/proxy',
  statusCode
);
```

### **2.4 Controller Consolidation - COMPLETED**
- ✅ **Deleted fragmented controllers**: Removed auth-proxy, auth-protected-proxy, users-proxy controllers
- ✅ **Created DynamicProxyController**: Single controller with centralized route configuration
- ✅ **Implemented route-based auth**: Dynamic JWT validation based on route requirements

### **2.5 Middleware Cleanup - COMPLETED**
- ✅ **Removed correlation middleware**: Deleted redundant custom correlation handling
- ✅ **Deleted auth middleware**: Removed old jwks-rsa based auth middleware
- ✅ **Updated proxy module**: Now uses only centralized libraries

### **2.6 Error Handling Integration - COMPLETED**
- ✅ **Updated proxy service**: Now uses ErrorResponseBuilderService from @libs/error-handling
- ✅ **Removed custom error handling**: Standardized error responses
- ✅ **Centralized error format**: Consistent error structure across services

### **2.7 HttpClientService Integration - COMPLETED**
- ✅ **Service-specific HTTP clients**: Proxy service uses pre-configured service clients
- ✅ **Got HTTP/2 integration**: Dynamic controller and proxy service use Got
- ✅ **Circuit breaker support**: Built into HttpClientService

## **Phase 2 Results Summary**
- **~138 lines of controller code → Single dynamic controller**
- **3 fragmented controllers → 1 unified controller**  
- **Custom middleware → Centralized @libs/error-handling**
- **Scattered auth logic → Route-based auth validation**
- **Custom error handling → Standardized error responses**

## 🎯 **Phase 3: Testing & Validation - PLANNED**

### **3.1 Functionality Testing**
- [ ] Test dynamic routing with auth requirements
- [ ] Verify service-to-service communication works
- [ ] Test error handling consistency

### **3.2 Performance Validation**
- [ ] Measure HTTP/2 performance improvements
- [ ] Validate circuit breaker functionality
- [ ] Test retry logic and timeouts

### **3.3 Node.js Version Update**
- [ ] Update base Docker image to Node 22 LTS (required for Got)
- [ ] Test compatibility with existing services
- [ ] Update development environment

## 📊 **Progress Tracking**

### **Completed**
- ✅ **Core @libs/http architecture** - removed hardcoded service configs
- ✅ **Centralized Keycloak client** - ~500 lines of duplicate code eliminated
- ✅ **BaseServiceClient pattern** - ~60% reduction in service client boilerplate
- ✅ **Auth service integration** - 748→250 lines using centralized clients
- ✅ **API Gateway HTTP module** - service-specific configuration created
- ✅ **Dynamic controller implementation** - replaced 3 controllers with 1 unified controller
- ✅ **Proxy service simplification** - using Got with centralized error handling
- ✅ **Architecture cleanup** - removed redundant middleware and auth handling
- ✅ **Centralized route configuration** - declarative routing with auth requirements

### **Pending**
- ⏳ **Node 22 upgrade** - for Got compatibility and testing
- ⏳ **Performance testing** - validate HTTP/2 improvements
- ⏳ **Integration testing** - test dynamic routing with real services

## 🎉 **Expected Benefits**

### **Performance**
- **60-80% HTTP performance improvement** via Got HTTP/2
- **50-80% request reduction** via intelligent caching
- **Optimized service-specific configurations** (auth fast, external APIs resilient)

### **Code Quality**
- **~1000+ lines removed** (redundant HTTP code)
- **75% reduction in controller code** (dynamic routing)
- **Centralized configuration** (no service knowledge in core libs)

### **Maintainability**
- **Single source of truth** for routes and auth requirements
- **Service-specific HTTP configurations** without core library changes
- **Unified error handling** and observability

## 🚨 **Critical Decisions Made**

1. **No backward compatibility** - complete rewrite approach for manageable codebase
2. **Service-specific HTTP modules** - avoid hardcoding in core libraries
3. **Dynamic proxy controller** - eliminate controller proliferation
4. **Comprehensive observability** - maintain detailed logging throughout

## 🔗 **Related Documents**

- `analysis-got-migration-comprehensive-impact.md` - Detailed architecture analysis
- `COMPREHENSIVE-GOT-MIGRATION-PLAN.md` - Original migration planning
- `libs/http/notes-*.md` - Implementation patterns and best practices
- `docs/task-management-guidelines.md` - Task tracking approach

---

**Next Session**: Continue with Phase 2.4 - implement dynamic controller and remove redundant middleware.