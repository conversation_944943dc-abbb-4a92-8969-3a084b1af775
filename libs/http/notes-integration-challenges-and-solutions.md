# Integration Challenges and Solutions: Got HTTP Module

## Critical Integration Challenges

### 1. **NestJS Ecosystem Integration**

#### **Challenge**
Got doesn't have official NestJS integration like `@nestjs/axios`, requiring custom module creation and dependency injection setup.

#### **Solution**
```typescript
// Custom NestJS module for Got
@Module({})
export class HttpModule {
  static forRoot(options: GotHttpModuleOptions = {}): DynamicModule {
    return {
      module: HttpModule,
      providers: [
        {
          provide: 'HTTP_MODULE_OPTIONS',
          useValue: options
        },
        {
          provide: HttpClientService,
          useFactory: (
            configService: ConfigService,
            observabilityService: ObservabilityService,
            options: GotHttpModuleOptions
          ) => {
            return new HttpClientService(configService, observabilityService, options);
          },
          inject: [ConfigService, ObservabilityService, 'HTTP_MODULE_OPTIONS']
        }
      ],
      exports: [HttpClientService],
      global: true
    };
  }

  static forRootAsync(options: GotHttpModuleAsyncOptions): DynamicModule {
    return {
      module: HttpModule,
      imports: options.imports || [],
      providers: [
        {
          provide: 'HTTP_MODULE_OPTIONS',
          useFactory: options.useFactory,
          inject: options.inject || []
        },
        {
          provide: HttpClientService,
          useFactory: (
            configService: ConfigService,
            observabilityService: ObservabilityService,
            moduleOptions: GotHttpModuleOptions
          ) => {
            return new HttpClientService(configService, observabilityService, moduleOptions);
          },
          inject: [ConfigService, ObservabilityService, 'HTTP_MODULE_OPTIONS']
        }
      ],
      exports: [HttpClientService],
      global: true
    };
  }
}

// Usage in app modules
@Module({
  imports: [
    HttpModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: {
          response: configService.get<number>('HTTP_TIMEOUT', 5000)
        },
        retry: {
          limit: configService.get<number>('HTTP_RETRY_LIMIT', 3)
        },
        http2: configService.get<boolean>('HTTP2_ENABLED', true),
        cache: configService.get<boolean>('HTTP_CACHE_ENABLED', true)
      }),
      inject: [ConfigService]
    })
  ]
})
export class AppModule {}
```

### 2. **Testing and Mocking Strategy**

#### **Challenge**
Got doesn't have the extensive mocking ecosystem that Axios has (like axios-mock-adapter). Testing HTTP interactions becomes more complex.

#### **Solution A: Custom Mock Implementation**
```typescript
// Custom Got mock for testing
export class GotMockAdapter {
  private mocks = new Map<string, MockResponse>();
  private originalGot: any;

  constructor(private gotInstance: any) {
    this.originalGot = gotInstance;
  }

  onGet(url: string | RegExp): MockChain {
    return this.addMock('GET', url);
  }

  onPost(url: string | RegExp): MockChain {
    return this.addMock('POST', url);
  }

  private addMock(method: string, url: string | RegExp): MockChain {
    const key = `${method}:${url.toString()}`;
    
    return {
      reply: (status: number, data?: any, headers?: Record<string, string>) => {
        this.mocks.set(key, { status, data, headers });
        return this;
      },
      replyWithError: (error: Error) => {
        this.mocks.set(key, { error });
        return this;
      }
    };
  }

  install() {
    // Override Got instance methods
    const mockHandler = async (url: string, options: any = {}) => {
      const method = options.method || 'GET';
      const key = `${method}:${url}`;
      
      const mock = this.findMock(key);
      if (mock) {
        if (mock.error) {
          throw mock.error;
        }
        return {
          statusCode: mock.status,
          body: mock.data,
          headers: mock.headers || {}
        };
      }
      
      // Fallback to original implementation or throw
      throw new Error(`No mock found for ${method} ${url}`);
    };

    // Replace Got methods
    this.gotInstance.get = mockHandler;
    this.gotInstance.post = mockHandler;
    this.gotInstance.put = mockHandler;
    this.gotInstance.delete = mockHandler;
    this.gotInstance.patch = mockHandler;
  }

  restore() {
    // Restore original Got instance
    Object.assign(this.gotInstance, this.originalGot);
  }

  private findMock(key: string): MockResponse | undefined {
    // Exact match first
    if (this.mocks.has(key)) {
      return this.mocks.get(key);
    }

    // RegExp matching
    for (const [mockKey, response] of this.mocks.entries()) {
      try {
        const [method, pattern] = mockKey.split(':');
        if (key.startsWith(method + ':')) {
          const url = key.substring(method.length + 1);
          if (new RegExp(pattern).test(url)) {
            return response;
          }
        }
      } catch (e) {
        // Not a RegExp pattern, skip
      }
    }

    return undefined;
  }
}

// Usage in tests
describe('UserService', () => {
  let userService: UserService;
  let httpMock: GotMockAdapter;

  beforeEach(() => {
    const module = Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: HttpClientService,
          useValue: {
            get: jest.fn(),
            post: jest.fn()
          }
        }
      ]
    }).compile();

    userService = module.get<UserService>(UserService);
    const httpClient = module.get<HttpClientService>(HttpClientService);
    httpMock = new GotMockAdapter(httpClient);
    httpMock.install();
  });

  it('should fetch user data', async () => {
    httpMock.onGet('/users/123').reply(200, { id: 123, name: 'John' });
    
    const user = await userService.getUser(123);
    expect(user.name).toBe('John');
  });
});
```

#### **Solution B: Using Undici MockAgent (Recommended)**
```typescript
// More sophisticated mocking using undici MockAgent
import { MockAgent, setGlobalDispatcher } from 'undici';

export class UndiciMockSetup {
  private mockAgent: MockAgent;
  private mockPools = new Map<string, any>();

  setup() {
    this.mockAgent = new MockAgent();
    setGlobalDispatcher(this.mockAgent);
    
    // Disable real network connections during tests
    this.mockAgent.disableNetConnect();
  }

  teardown() {
    this.mockAgent?.close();
  }

  mockService(baseUrl: string) {
    const mockPool = this.mockAgent.get(baseUrl);
    this.mockPools.set(baseUrl, mockPool);
    return {
      get: (path: string) => ({
        reply: (status: number, data: any) => {
          mockPool.intercept({
            path,
            method: 'GET'
          }).reply(status, data);
        }
      }),
      post: (path: string) => ({
        reply: (status: number, data: any) => {
          mockPool.intercept({
            path,
            method: 'POST'
          }).reply(status, data);
        }
      })
    };
  }
}

// Usage in tests
describe('Integration Tests', () => {
  let mockSetup: UndiciMockSetup;

  beforeEach(() => {
    mockSetup = new UndiciMockSetup();
    mockSetup.setup();
  });

  afterEach(() => {
    mockSetup.teardown();
  });

  it('should handle user creation', async () => {
    const userServiceMock = mockSetup.mockService('http://user-service:3000');
    userServiceMock.post('/users').reply(201, { id: 123, name: 'John' });

    // Test code using real Got instance
    const response = await httpClient.post('http://user-service:3000/users', {
      json: { name: 'John' }
    });

    expect(response.statusCode).toBe(201);
  });
});
```

### 3. **Error Handling Compatibility**

#### **Challenge**
Got uses different error types and structures compared to Axios, requiring updates to existing error handling logic.

#### **Solution: Error Adaptation Layer**
```typescript
// Error adapter to maintain compatibility
export class HttpErrorAdapter {
  static adaptGotError(gotError: any): AxiosLikeError {
    // Convert Got error to Axios-like structure for compatibility
    const adapted = {
      name: gotError.name,
      message: gotError.message,
      code: gotError.code,
      config: {
        method: gotError.options?.method,
        url: gotError.options?.url?.toString(),
        data: gotError.options?.json || gotError.options?.body,
        headers: gotError.options?.headers
      },
      request: gotError.request,
      response: gotError.response ? {
        status: gotError.response.statusCode,
        statusText: gotError.response.statusMessage,
        headers: gotError.response.headers,
        data: gotError.response.body,
        statusCode: gotError.response.statusCode // Keep Got property too
      } : undefined,
      isAxiosError: false, // Mark as adapted
      isGotError: true
    };

    return adapted;
  }

  static isRetryableError(error: any): boolean {
    // Unified retry logic for both error types
    if (error.isGotError || error.name === 'RequestError' || error.name === 'HTTPError') {
      // Got error handling
      return error.code === 'ECONNRESET' || 
             error.code === 'ETIMEDOUT' || 
             (error.response?.statusCode >= 500);
    }

    // Fallback to Axios-style error checking
    return !error.response || error.response.status >= 500;
  }
}

// Enhanced HttpClientService with error adaptation
export class HttpClientService {
  async get<T>(url: string, options: HttpClientOptions = {}): Promise<T> {
    try {
      const response = await this.gotInstance.get(url, this.adaptOptions(options));
      return response.body;
    } catch (error) {
      // Adapt error for existing error handlers
      throw HttpErrorAdapter.adaptGotError(error);
    }
  }

  private adaptOptions(options: HttpClientOptions): GotOptions {
    return {
      searchParams: options.params,
      json: options.data,
      headers: options.headers,
      timeout: { response: options.timeout },
      retry: {
        limit: options.retries || 0
      },
      // Map other options...
    };
  }
}
```

### 4. **Performance Optimization Challenges**

#### **Challenge**
Need to optimize Got configuration for different service communication patterns while maintaining simplicity.

#### **Solution: Service-Specific Client Factory**
```typescript
export class OptimizedHttpClientFactory {
  private clientCache = new Map<string, Got>();

  createClientForService(serviceName: string, config: ServiceHttpConfig): Got {
    const cacheKey = `${serviceName}:${JSON.stringify(config)}`;
    
    if (this.clientCache.has(cacheKey)) {
      return this.clientCache.get(cacheKey)!;
    }

    const client = got.extend({
      // Base configuration
      prefixUrl: config.baseUrl,
      http2: config.http2 ?? true,
      
      // Service-specific optimizations
      ...this.getServiceOptimizations(serviceName, config),
      
      // Common hooks
      hooks: {
        beforeRequest: [
          this.addObservabilityHook(serviceName),
          this.addAuthenticationHook(),
          this.addCorrelationHook()
        ],
        afterResponse: [
          this.addMetricsHook(serviceName),
          this.addCacheHook(serviceName)
        ],
        beforeError: [
          this.addErrorHandlingHook(serviceName)
        ]
      }
    });

    this.clientCache.set(cacheKey, client);
    return client;
  }

  private getServiceOptimizations(serviceName: string, config: ServiceHttpConfig): Partial<GotOptions> {
    switch (serviceName) {
      case 'auth-service':
        return {
          timeout: { response: 3000 }, // Fast auth responses
          retry: { limit: 2 }, // Quick failure for auth
          cache: false // Don't cache auth responses
        };

      case 'user-service':
        return {
          timeout: { response: 5000 },
          retry: { limit: 3 },
          cache: true, // Cache user data
          cacheOptions: {
            immutableMinTimeToLive: 60000 // 1 minute cache
          }
        };

      case 'external-api':
        return {
          timeout: { response: 10000 }, // External APIs can be slower
          retry: { 
            limit: 5,
            calculateDelay: ({attemptCount}) => Math.pow(2, attemptCount) * 1000
          },
          cache: true,
          cacheOptions: {
            immutableMinTimeToLive: 300000 // 5 minute cache
          }
        };

      default:
        return {
          timeout: { response: 5000 },
          retry: { limit: 3 }
        };
    }
  }
}

// Usage
const factory = new OptimizedHttpClientFactory();

// Fast, reliable auth client
const authClient = factory.createClientForService('auth-service', {
  baseUrl: 'http://auth-service:3001'
});

// Cacheable user client
const userClient = factory.createClientForService('user-service', {
  baseUrl: 'http://user-service:3002'
});
```

### 5. **Configuration Management Challenges**

#### **Challenge**
Need to manage complex Got configurations while maintaining environment-specific settings and service-specific overrides.

#### **Solution: Hierarchical Configuration System**
```typescript
// Hierarchical configuration with environment overrides
export interface HttpConfiguration {
  global: GotGlobalConfig;
  services: Record<string, ServiceSpecificConfig>;
  environments: Record<string, EnvironmentOverrides>;
}

export class HttpConfigurationManager {
  private config: HttpConfiguration;

  constructor(private configService: ConfigService) {
    this.config = this.loadConfiguration();
  }

  private loadConfiguration(): HttpConfiguration {
    const environment = this.configService.get('NODE_ENV', 'development');
    
    const baseConfig = {
      global: {
        http2: this.configService.get<boolean>('HTTP2_ENABLED', true),
        timeout: {
          response: this.configService.get<number>('HTTP_TIMEOUT', 5000),
          connect: this.configService.get<number>('HTTP_CONNECT_TIMEOUT', 2000)
        },
        retry: {
          limit: this.configService.get<number>('HTTP_RETRY_LIMIT', 3),
          methods: ['GET', 'PUT', 'HEAD', 'DELETE', 'OPTIONS', 'TRACE'],
          statusCodes: [408, 413, 429, 500, 502, 503, 504, 521, 522, 524]
        },
        cache: {
          enabled: this.configService.get<boolean>('HTTP_CACHE_ENABLED', true),
          ttl: this.configService.get<number>('HTTP_CACHE_TTL', 300000)
        }
      },
      services: this.loadServiceConfigurations(),
      environments: this.loadEnvironmentOverrides()
    };

    // Apply environment-specific overrides
    return this.applyEnvironmentOverrides(baseConfig, environment);
  }

  private loadServiceConfigurations(): Record<string, ServiceSpecificConfig> {
    return {
      'auth-service': {
        timeout: { response: 3000 },
        retry: { limit: 2 },
        cache: { enabled: false },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 5,
          resetTimeout: 30000
        }
      },
      'user-service': {
        timeout: { response: 5000 },
        retry: { limit: 3 },
        cache: { 
          enabled: true, 
          ttl: 60000 
        },
        circuitBreaker: {
          enabled: true,
          failureThreshold: 10,
          resetTimeout: 60000
        }
      },
      'external-payment-api': {
        timeout: { response: 15000 },
        retry: { 
          limit: 5,
          calculateDelay: ({attemptCount}) => Math.min(Math.pow(2, attemptCount) * 1000, 30000)
        },
        cache: { enabled: false }, // Never cache payment responses
        circuitBreaker: {
          enabled: true,
          failureThreshold: 3, // Fail fast for payments
          resetTimeout: 120000
        }
      }
    };
  }

  private loadEnvironmentOverrides(): Record<string, EnvironmentOverrides> {
    return {
      development: {
        global: {
          timeout: { response: 30000 }, // Longer timeouts for debugging
          retry: { limit: 1 }, // Fewer retries for faster feedback
          cache: { enabled: false } // Disable cache in development
        },
        logging: {
          level: 'debug',
          includeRequestBody: true,
          includeResponseBody: true
        }
      },
      test: {
        global: {
          timeout: { response: 1000 }, // Fast timeouts for tests
          retry: { limit: 0 }, // No retries in tests
          cache: { enabled: false }
        }
      },
      production: {
        global: {
          timeout: { response: 5000 },
          retry: { limit: 3 },
          cache: { enabled: true }
        },
        logging: {
          level: 'info',
          includeRequestBody: false,
          includeResponseBody: false
        }
      }
    };
  }

  getServiceConfig(serviceName: string): GotOptions {
    const globalConfig = this.config.global;
    const serviceConfig = this.config.services[serviceName] || {};
    
    // Merge configurations with service-specific overrides
    return this.mergeConfigurations(globalConfig, serviceConfig);
  }

  private mergeConfigurations(global: any, service: any): GotOptions {
    return {
      http2: service.http2 ?? global.http2,
      timeout: {
        response: service.timeout?.response ?? global.timeout.response,
        connect: service.timeout?.connect ?? global.timeout.connect
      },
      retry: {
        limit: service.retry?.limit ?? global.retry.limit,
        methods: service.retry?.methods ?? global.retry.methods,
        statusCodes: service.retry?.statusCodes ?? global.retry.statusCodes,
        calculateDelay: service.retry?.calculateDelay ?? global.retry.calculateDelay
      },
      cache: service.cache?.enabled && global.cache.enabled ? {
        // Cache configuration
      } : false
    };
  }
}
```

## Summary of Solutions

### **Integration Complexity Scoring**

| Challenge | Complexity (1-5) | Solution Quality | Migration Risk |
|-----------|------------------|------------------|----------------|
| NestJS Integration | 3 | High | Low |
| Testing/Mocking | 4 | Medium | Medium |
| Error Handling | 2 | High | Low |
| Performance Optimization | 3 | High | Low |
| Configuration Management | 3 | High | Low |

### **Mitigation Strategies**

1. **Parallel Development**: Build Got module alongside existing Axios module
2. **Comprehensive Testing**: Create extensive test suite before migration
3. **Gradual Migration**: Service-by-service migration with rollback capability
4. **Team Training**: Dedicated training sessions on Got API and patterns
5. **Documentation**: Comprehensive migration guide and best practices

### **Success Criteria**

✅ **No breaking changes** to service APIs
✅ **Improved performance** metrics (latency, throughput)
✅ **Reduced codebase** complexity (~1000 lines removed)
✅ **Enhanced observability** and debugging capabilities
✅ **Better error handling** and retry logic

The challenges are manageable and the solutions provide significant long-term value while maintaining system stability during migration.