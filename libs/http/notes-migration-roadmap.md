# Migration Roadmap: Axios to Got HTTP Module

## Phase-by-Phase Migration Strategy

### **Phase 1: Foundation (Days 1-2)**

#### **1.1 New HTTP Module Creation**
```bash
# Day 1 Morning: Setup new module structure
libs/http/
├── src/
│   ├── client/
│   │   ├── http-client.service.ts
│   │   ├── http-client.interfaces.ts
│   │   └── http-client.factory.ts
│   ├── configuration/
│   │   ├── http-config.service.ts
│   │   └── http-config.interfaces.ts
│   ├── observability/
│   │   ├── http-observability.hooks.ts
│   │   └── http-metrics.service.ts
│   ├── testing/
│   │   ├── http-mock.adapter.ts
│   │   └── http-test.utilities.ts
│   ├── http.module.ts
│   └── index.ts
├── package.json
├── tsconfig.json
└── README.md
```

#### **1.2 Core Implementation**
```typescript
// Day 1: Core HttpClientService implementation
export class HttpClientService {
  private gotInstance: Got;
  private configService: HttpConfigService;
  private observabilityHooks: HttpObservabilityHooks;

  constructor(
    config: HttpConfiguration,
    observability: ObservabilityService
  ) {
    this.configService = new HttpConfigService(config);
    this.observabilityHooks = new HttpObservabilityHooks(observability);
    this.gotInstance = this.createGotInstance();
  }

  private createGotInstance(): Got {
    return got.extend({
      ...this.configService.getGlobalConfig(),
      hooks: this.observabilityHooks.getAllHooks()
    });
  }

  // Axios-compatible interface for easier migration
  async get<T>(url: string, config?: HttpRequestConfig): Promise<HttpResponse<T>> {
    const gotOptions = this.adaptAxiosConfigToGot(config);
    const response = await this.gotInstance.get(url, gotOptions);
    return this.adaptGotResponseToAxios(response);
  }

  async post<T>(url: string, data?: any, config?: HttpRequestConfig): Promise<HttpResponse<T>> {
    const gotOptions = this.adaptAxiosConfigToGot(config, data);
    const response = await this.gotInstance.post(url, gotOptions);
    return this.adaptGotResponseToAxios(response);
  }

  // Got-native interface for new code
  createServiceClient(serviceName: string): ServiceHttpClient {
    const serviceConfig = this.configService.getServiceConfig(serviceName);
    return new ServiceHttpClient(serviceName, serviceConfig, this.observabilityHooks);
  }
}
```

#### **1.3 Testing Infrastructure**
```typescript
// Day 1 Afternoon: Testing utilities
export class HttpTestUtils {
  static createMockHttpModule(mockResponses: MockHttpResponses = {}): DynamicModule {
    return {
      module: class MockHttpModule {},
      providers: [
        {
          provide: HttpClientService,
          useValue: new MockHttpClientService(mockResponses)
        }
      ],
      exports: [HttpClientService]
    };
  }

  static setupUndiciMocks(): UndiciMockSetup {
    const mockAgent = new MockAgent();
    setGlobalDispatcher(mockAgent);
    mockAgent.disableNetConnect();
    
    return new UndiciMockSetup(mockAgent);
  }
}

// Integration test template
describe('HTTP Module Integration', () => {
  let httpClient: HttpClientService;
  let mockSetup: UndiciMockSetup;

  beforeEach(() => {
    mockSetup = HttpTestUtils.setupUndiciMocks();
    httpClient = new HttpClientService(testConfig, mockObservability);
  });

  afterEach(() => {
    mockSetup.teardown();
  });

  it('should make successful GET request', async () => {
    mockSetup.mockService('http://test-service')
      .get('/test').reply(200, { success: true });

    const response = await httpClient.get('http://test-service/test');
    expect(response.data.success).toBe(true);
  });
});
```

#### **1.4 Documentation**
```markdown
# Day 2: Complete documentation
- API Reference
- Migration Guide
- Configuration Guide
- Best Practices
- Troubleshooting Guide
```

### **Phase 2: Parallel Integration (Days 3-4)**

#### **2.1 Side-by-Side Implementation**
```typescript
// Day 3: Install new HTTP module alongside old one
@Module({
  imports: [
    // Old module (keep for now)
    NestjsCommonModule,
    
    // New module (for testing)
    HttpModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        services: {
          'user-service': {
            baseUrl: configService.get('USER_SERVICE_URL'),
            timeout: { response: 5000 },
            retry: { limit: 3 },
            cache: { enabled: true }
          },
          'auth-service': {
            baseUrl: configService.get('AUTH_SERVICE_URL'),
            timeout: { response: 3000 },
            retry: { limit: 2 },
            cache: { enabled: false }
          }
        }
      }),
      inject: [ConfigService]
    })
  ]
})
export class AppModule {}
```

#### **2.2 Feature Flag Implementation**
```typescript
// Day 3: Feature flag for gradual rollout
export class FeatureFlaggedHttpService {
  constructor(
    @Inject('OLD_HTTP_CLIENT') private oldHttpClient: AxiosHttpClient,
    @Inject('NEW_HTTP_CLIENT') private newHttpClient: HttpClientService,
    private configService: ConfigService
  ) {}

  async makeRequest<T>(method: string, url: string, options: any = {}): Promise<T> {
    const useNewHttpClient = this.configService.get<boolean>('USE_GOT_HTTP_CLIENT', false);
    
    if (useNewHttpClient) {
      return this.newHttpClient[method.toLowerCase()](url, options);
    } else {
      return this.oldHttpClient[method.toLowerCase()](url, options);
    }
  }
}
```

#### **2.3 A/B Testing Setup**
```typescript
// Day 4: A/B testing for performance comparison
export class HttpPerformanceComparator {
  async comparePerformance(testCases: HttpTestCase[]): Promise<ComparisonResults> {
    const results = {
      axios: { totalTime: 0, successCount: 0, errorCount: 0 },
      got: { totalTime: 0, successCount: 0, errorCount: 0 }
    };

    for (const testCase of testCases) {
      // Test with Axios
      const axiosResult = await this.timeRequest(() => 
        this.axiosClient.request(testCase.config)
      );
      
      // Test with Got
      const gotResult = await this.timeRequest(() => 
        this.gotClient.request(testCase.url, testCase.options)
      );

      // Collect metrics
      this.updateResults(results.axios, axiosResult);
      this.updateResults(results.got, gotResult);
    }

    return this.generateComparisonReport(results);
  }
}
```

### **Phase 3: Service Migration (Days 5-7)**

#### **3.1 Migration Order (Low Risk First)**
```typescript
// Day 5: Start with lowest-risk service
// 1. API Gateway Health Checks (lowest risk - internal only)
// 2. User Service Client (medium risk - has circuit breaker)
// 3. Keycloak Service (highest risk - auth critical)

// Migration template for each service
class ServiceMigrationPlan {
  async migrateHealthChecks() {
    // Step 1: Update health controller
    // Step 2: Test health endpoints
    // Step 3: Verify observability
    // Step 4: Performance validation
  }

  async migrateUserServiceClient() {
    // Step 1: Update client implementation
    // Step 2: Update circuit breaker integration
    // Step 3: Test user operations
    // Step 4: Validate auth propagation
  }

  async migrateKeycloakService() {
    // Step 1: Update authentication flows
    // Step 2: Test login/logout/refresh
    // Step 3: Validate error handling
    // Step 4: Performance testing
  }
}
```

#### **3.2 Service-by-Service Migration**

##### **Day 5: API Gateway Health Checks**
```typescript
// Before (Axios)
const response = await this.httpClientService.makeRequest(
  serviceName, 
  'get', 
  healthUrl,
  undefined,
  { 'Accept': 'application/json' }
);

// After (Got)
const response = await this.httpClientService.get(healthUrl, {
  serviceName,
  headers: { 'Accept': 'application/json' },
  timeout: { response: 5000 },
  retry: { limit: 2 }
});

// Validation Steps:
// 1. Test all health endpoints
// 2. Verify response format unchanged
// 3. Check observability metrics
// 4. Validate error handling
```

##### **Day 6: User Service Client**
```typescript
// Before (Complex circuit breaker integration)
return this.userServiceCircuit.execute(async () => {
  return this.httpClient.makeRequest<T>('UserService', method, url, data, customHeaders);
});

// After (Simplified with Got retry + circuit breaker fallback)
const userClient = this.httpClientFactory.createServiceClient('user-service');

try {
  return await userClient[method.toLowerCase()](path, {
    json: data,
    headers: customHeaders,
    retry: { limit: 3 }, // Got handles retries
    timeout: { response: 5000 }
  });
} catch (error) {
  // Circuit breaker only for final fallback
  if (error.name === 'RetryError') {
    return this.userServiceCircuit.handleFinalFailure(error);
  }
  throw error;
}

// Validation Steps:
// 1. Test CRUD operations
// 2. Verify circuit breaker behavior
// 3. Test retry scenarios
// 4. Check auth header propagation
```

##### **Day 7: Keycloak Service**
```typescript
// Enhanced Keycloak service with Got optimizations
class EnhancedKeycloakService {
  private keycloakClient: ServiceHttpClient;

  constructor() {
    this.keycloakClient = httpClientFactory.createServiceClient('keycloak', {
      baseUrl: process.env.KEYCLOAK_BASE_URL,
      timeout: { response: 5000 },
      retry: { 
        limit: 2, // Auth should fail fast
        statusCodes: [500, 502, 503, 504] // Don't retry 4xx auth errors
      },
      cache: {
        enabled: true,
        // Cache JWKS responses
        cacheOptions: {
          immutableMinTimeToLive: 3600000 // 1 hour
        }
      }
    });
  }

  async authenticateUser(email: string, password: string): Promise<KeycloakTokenResponse> {
    const tokenUrl = `/realms/${this.realm}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'password',
      client_id: this.clientId,
      client_secret: this.clientSecret,
      username: email,
      password: password
    });

    // Got automatically handles error transformation via hooks
    const response = await this.keycloakClient.post(tokenUrl, {
      body: params.toString(),
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    });

    return response.body;
  }
}

// Validation Steps:
// 1. Test authentication flows
// 2. Verify error responses (401, 403, etc.)
// 3. Test token refresh
// 4. Validate JWKS caching
// 5. Performance testing
```

### **Phase 4: Optimization & Cleanup (Days 8-9)**

#### **4.1 Performance Optimization**
```typescript
// Day 8: Enable advanced features
const optimizedConfig = {
  global: {
    http2: true, // Enable HTTP/2
    cache: {
      enabled: true,
      sharedCache: redisCache // Use Redis for shared caching
    }
  },
  services: {
    'user-service': {
      cache: { ttl: 60000 }, // Cache user data for 1 minute
      http2: true
    },
    'external-apis': {
      retry: { 
        limit: 5,
        calculateDelay: ({attemptCount}) => Math.pow(2, attemptCount) * 1000
      }
    }
  }
};

// Performance testing
const performanceTests = [
  { name: 'Single Request', test: () => httpClient.get('/users/123') },
  { name: 'Parallel Requests', test: () => Promise.all([
    httpClient.get('/users/1'),
    httpClient.get('/users/2'),
    httpClient.get('/users/3')
  ])},
  { name: 'Cached Requests', test: () => Promise.all([
    httpClient.get('/static-data'),
    httpClient.get('/static-data'), // Should hit cache
    httpClient.get('/static-data')  // Should hit cache
  ])}
];
```

#### **4.2 Remove Legacy Code**
```bash
# Day 9: Clean up old HTTP module
rm -rf libs/nestjs-common/src/http/
git rm libs/nestjs-common/src/http/http-client.service.ts
git rm libs/nestjs-common/src/http/http-observability.interceptor.ts
git rm libs/nestjs-common/src/http/http-observability-customizer.service.ts
git rm libs/nestjs-common/src/http/http.module.ts

# Update package.json files
# Remove: axios, @nestjs/axios
# Add: got
```

#### **4.3 Update Dependencies**
```json
// Root package.json updates
{
  "dependencies": {
    "got": "^12.6.1",
    // Remove these:
    // "axios": "^1.0.0",
    // "@nestjs/axios": "^3.0.0"
  }
}

// Service package.json updates
{
  "dependencies": {
    "@libs/http": "workspace:*"
    // Remove: "@libs/nestjs-common"
  }
}
```

### **Phase 5: Validation & Documentation (Days 10-11)**

#### **5.1 Comprehensive Testing**
```typescript
// Day 10: Full system testing
describe('End-to-End HTTP Migration Validation', () => {
  it('should handle complete user journey', async () => {
    // 1. Register user (auth-service + user-service)
    const registerResponse = await request(app)
      .post('/auth/register')
      .send({ email: '<EMAIL>', password: 'password123' });
    
    // 2. Login user (auth-service + keycloak)
    const loginResponse = await request(app)
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    
    // 3. Access protected resource (api-gateway + user-service)
    const userResponse = await request(app)
      .get('/users/profile')
      .set('Authorization', `Bearer ${loginResponse.body.access_token}`);
    
    // Verify all requests completed successfully
    expect(registerResponse.status).toBe(201);
    expect(loginResponse.status).toBe(200);
    expect(userResponse.status).toBe(200);
  });

  it('should maintain performance benchmarks', async () => {
    const benchmark = new PerformanceBenchmark();
    const results = await benchmark.runFullSuite();
    
    // Verify performance improvements
    expect(results.averageResponseTime).toBeLessThan(previousBenchmark.averageResponseTime);
    expect(results.cacheHitRatio).toBeGreaterThan(0.5);
    expect(results.http2Usage).toBeGreaterThan(0.8);
  });
});
```

#### **5.2 Performance Validation**
```typescript
// Day 10: Performance metrics validation
const performanceMetrics = {
  beforeMigration: {
    averageResponseTime: 250, // ms
    p95ResponseTime: 500,
    errorRate: 0.02,
    memoryUsage: 450 // MB
  },
  afterMigration: {
    averageResponseTime: 175, // 30% improvement
    p95ResponseTime: 350,     // 30% improvement
    errorRate: 0.01,          // 50% improvement
    memoryUsage: 380,         // 15% improvement
    cacheHitRatio: 0.65,      // New metric
    http2Usage: 0.85          // New metric
  }
};
```

#### **5.3 Documentation Updates**
```markdown
# Day 11: Complete documentation
1. Updated API documentation
2. Migration guide for future services
3. Performance tuning guide
4. Troubleshooting guide
5. Best practices documentation
```

### **Rollback Strategy**

#### **Emergency Rollback (if needed)**
```bash
# Quick rollback process (< 30 minutes)
1. Revert package.json changes
2. Restore old HTTP module
3. Revert service imports
4. Restart services
5. Verify system health

# Rollback commands
git revert <migration-commits>
yarn install
yarn build:libs
docker-compose restart
```

#### **Partial Rollback**
```typescript
// Service-by-service rollback capability
@Injectable()
export class HttpClientSelector {
  constructor(
    @Inject('OLD_HTTP_CLIENT') private oldClient: any,
    @Inject('NEW_HTTP_CLIENT') private newClient: any,
    private configService: ConfigService
  ) {}

  getClient(serviceName: string) {
    const useNewClient = this.configService.get(`USE_NEW_HTTP_CLIENT_${serviceName}`, true);
    return useNewClient ? this.newClient : this.oldClient;
  }
}
```

## Risk Mitigation

### **High Priority Risks**
1. **Authentication failures**: Extensive auth testing in staging
2. **Performance degradation**: Continuous performance monitoring
3. **Service disruption**: Gradual rollout with immediate rollback capability
4. **Data corruption**: Read-only validation before write operations

### **Medium Priority Risks**
1. **Error handling changes**: Comprehensive error scenario testing
2. **Caching issues**: Cache validation and invalidation testing
3. **Configuration errors**: Environment-specific config validation

### **Success Metrics**
- ✅ **Zero downtime** during migration
- ✅ **30% performance improvement** in response times
- ✅ **50% reduction** in HTTP-related errors
- ✅ **1000+ lines** of code removed
- ✅ **All tests passing** post-migration

## Timeline Summary

| Phase | Duration | Key Deliverables | Risk Level |
|-------|----------|------------------|------------|
| **Foundation** | 2 days | New HTTP module, tests, docs | Low |
| **Parallel Integration** | 2 days | A/B testing, feature flags | Low |
| **Service Migration** | 3 days | All services migrated | Medium |
| **Optimization** | 2 days | Performance tuning, cleanup | Low |
| **Validation** | 2 days | Testing, documentation | Low |

**Total: 11 days with built-in buffer time**

This migration strategy ensures minimal risk while maximizing the benefits of adopting Got for our HTTP layer.